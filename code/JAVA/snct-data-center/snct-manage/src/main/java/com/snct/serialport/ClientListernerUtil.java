package com.snct.serialport;

import com.snct.common.core.redis.RedisCache;
import com.snct.common.utils.DateUtils;
import com.snct.system.domain.DataStatistics;
import com.snct.system.service.IDataStatisticsService;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

/**
 * class 监听里面抽出来的类
 *
 * <AUTHOR>
 */
@Component
public class ClientListernerUtil {

    private static final Logger logger = LoggerFactory.getLogger(ClientListernerUtil.class);
    @Autowired
   private RedisCache redisCache;
    @Autowired
    private IDataStatisticsService dataStatisticsService;
    public static boolean flag = true;
    public static int states = 0;//0预览 1解析预览
    public static boolean closeFlag = true;
    /**
     * 设备MAC
     */
    public static String sbMac = "";
    public static int type = 0;//模板类型
    /**
     * 收集采集数据长度-设备
     * @param contents
     */
    public synchronized void redisCompare(byte[] contents) {
        if (ArrayUtils.isEmpty(contents)){
            //如果没数据则不往下走，以免出现异常错误信息
            return;
        }
        Date nowDate = new Date();
        int cjLength = contents.length;
        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm",nowDate);
        try {
            long ct = DateUtils.parseDate(dateStr,"yyyy-MM-dd HH:mm").getTime();
            if (redisCache.getCacheObject("CJ_SB_"+ct)!=null){
                //已经存在该分钟的数据，继续往上累加
                int datas = redisCache.getCacheObject("CJ_SB_"+ct);
                redisCache.setCacheObject("CJ_SB_"+ct,datas+cjLength);//进行累加
            }else{
                //看看之前得是否有存在
                if (redisCache.keys("CJ_SB_*")!=null && redisCache.keys("CJ_SB_*").size()>0){
                    //存在得话找到所有循环
                    redisCache.keys("CJ_SB_*").iterator().forEachRemaining(s -> {
                        String longTime = s.split("_")[2];
                        Date date = new Date();
                        date.setTime(Long.parseLong(longTime));
                        //存在，新增数据库
                        int dataNew = redisCache.getCacheObject(s);
                        addStatis(dataNew,date,0);
                        //删除该条redis记录
                        redisCache.deleteObject(s);
                        //补齐之后到当前时间得每一条入库
                        try {
                            dateForAfter(date,nowDate,1);
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                    });
                }
                //新建新的redisobject，
                redisCache.setCacheObject("CJ_SB_"+ct,cjLength);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }
//        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm",new Date());
//        try {
//            long ct = DateUtils.parseDate(dateStr,"yyyy-MM-dd HH:mm").getTime();
//            if (redisCache.getCacheObject("CJ_"+ct)!=null){
//                //已经存在该分钟的数据，继续往上累加
//                int datas = redisCache.getCacheObject("CJ_"+ct);
//                redisCache.setCacheObject("CJ_"+ct,datas+cjLength);//进行累加
//            }else{
//                //判断前面的数据是否存在
//                Calendar beforeTime = Calendar.getInstance();
//                beforeTime.add(Calendar.MINUTE, -1);// 1分钟之前的时间
//                String dateLast = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm",beforeTime.getTime());
//                Date ct_before = DateUtils.parseDate(dateLast,"yyyy-MM-dd HH:mm");
//                long ct_last = ct_before.getTime();
//                if (redisCache.getCacheObject("CJ_"+ct_last)!=null){
//                    //存在，新增数据库
//                    int dataNew = redisCache.getCacheObject("CJ_"+ct_last);
//                    DataStatistics dataStatistics = new DataStatistics();
//                    dataStatistics.setCharacterLength(dataNew);//字符长度
//                    dataStatistics.setDataType(0);//采集数据
//                    dataStatistics.setDeviceType(0);//设备
//                    dataStatistics.setCreateTime(ct_before);
//                    dataStatisticsService.addDataStatistics(dataStatistics);
//                    //删除该条redis记录
//                    redisCache.deleteObject("CJ_"+ct_last);
//                }else {
//                    //不存在，暂时不做处理
//                }
//                //新建新的redisobject，
//                redisCache.setCacheObject("CJ_"+ct,cjLength);
//            }
//
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
    }

    /**
     * 收集采集数据长度-快照
     * @param cjLength
     */
    public synchronized void redisSnapshotCompare(int cjLength) {
        Date nowDate = new Date();
        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm",nowDate);
        try {
            long ct = DateUtils.parseDate(dateStr,"yyyy-MM-dd HH:mm").getTime();
            if (redisCache.getCacheObject("CJ_KZ_"+ct)!=null){
                //已经存在该分钟的数据，继续往上累加
                int datas = redisCache.getCacheObject("CJ_KZ_"+ct);
                redisCache.setCacheObject("CJ_KZ_"+ct,datas+cjLength);//进行累加
            }else{
                //看看之前得是否有存在
                if (redisCache.keys("CJ_KZ_*")!=null && redisCache.keys("CJ_KZ_*").size()>0){
                    //存在得话找到所有循环
                    redisCache.keys("CJ_KZ_*").iterator().forEachRemaining(s -> {
                        //先找到当前没入库得先入库
                        String longTime = s.split("_")[2];
                        Date date = new Date();
                        date.setTime(Long.parseLong(longTime));
                        //存在，新增数据库
                        int dataNew = redisCache.getCacheObject(s);
                        addStatis(dataNew,date,1);
                        //删除该条redis记录
                        redisCache.deleteObject(s);
                        //补齐之后到当前时间得每一条入库
                        try {
                            dateForAfter(date,nowDate,1);
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                    });
                }
                //新建新的redisobject，
                redisCache.setCacheObject("CJ_KZ_"+ct,cjLength);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    /**
     * 补齐每分钟得数据
     * @param date--redis上未入库得时间,nowDate-当前时间
     * @throws ParseException
     */
    public void dateForAfter(Date date,Date nowDate,Integer deviceType) throws ParseException {
        //当前时间
        String dateLast = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm", nowDate);
        Date ct_now = DateUtils.parseDate(dateLast, "yyyy-MM-dd HH:mm");
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(ct_now);
//        calendar1.add(Calendar.MINUTE, -1);
//        long between = (ct_now.getTime() - date.getTime()) / 1000;//换算成秒相减
//        long min = between % (24 * 3600) % 3600 / 60;//结果换算成差几分钟
//        if (min>1){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, 1);
//            for (int i = 0; i <min-1 ; i++) {
        while (calendar.getTime().before(calendar1.getTime())) {
            addStatis(0, calendar.getTime(),deviceType);
            calendar.add(Calendar.MINUTE, 1);
        }
//            }
//        }
    }

    public synchronized void addStatis(int dataNew,Date date,Integer deviceType){
        DataStatistics dataStatistics = new DataStatistics();
        dataStatistics.setCharacterLength(dataNew);//字符长度
        dataStatistics.setDataType(0);//采集数据
        dataStatistics.setDeviceType(deviceType);//快照
        dataStatistics.setCreateTime(date);
        dataStatisticsService.insertDataStatistics(dataStatistics);
    }

//    /**
//     * websocket发送实时数据
//     * @param amProtocol
//     */
//    public void webSocketSendMsg(AmProtocol amProtocol) {
//        int type = ClientListernerUtil.type;
//        if (DeviceTypeEnum.GPS_IN.getValue().equals(type) || DeviceTypeEnum.COMPASS_IN.getValue().equals(type)){
//            WebSocketServer.sendRealTimeData(bytesTo16String(amProtocol.getContent()), amProtocol.getMac());
//        }else{
//            WebSocketServer.sendRealTimeData(new String(amProtocol.getContent(), StandardCharsets.UTF_8), amProtocol.getMac());
//        }
//    }
//
//    public void parseView(AmProtocol amProtocol) {
//        int type = ClientListernerUtil.type;
////         amProtocol.setType(ClientListernerUtil.type);
//        String receiveMessage;
//        if (DeviceTypeEnum.ATTITUDE.getValue().equals(type)) {
//            // ATTITUDE
//            receiveMessage = bytesToTenString(amProtocol.getContent());
//        }else if (DeviceTypeEnum.GPS_IN.getValue().equals(type) || DeviceTypeEnum.COMPASS_IN.getValue().equals(type)) {
//            //内置设备
//            receiveMessage = bytesTo16String(amProtocol.getContent());
//        } else {
//            receiveMessage = new String(amProtocol.getContent(), StandardCharsets.UTF_8);
//            if (receiveMessage.contains("@@")) {
//                receiveMessage = receiveMessage.substring(8);
//            }
//        }
//        if (receiveMessage.contains("\n\t")){
//            receiveMessage = receiveMessage.replace("\n","");
//            //删除\t
//            receiveMessage = receiveMessage.replace("\t","");
//        }
//        KafkaMessage kafkaMessage = new KafkaMessage(type, DeviceUtil.getDeviceCode(amProtocol.getMac()), receiveMessage, 10, amProtocol.getTime());
//
//        logger.info(kafkaMessage+"解析预览"+"---"+flag);
//        //解析预览这边建list对象取10条kafkaMessage对象加入到集合对象
//        if (flag == false && amProtocol.getMac().equalsIgnoreCase(sbMac)){
//            if (DeviceTypeEnum.GPS_IN.getValue().equals(type) || DeviceTypeEnum.COMPASS_IN.getValue().equals(type)){
//                kafkaMessageList.add(kafkaMessage);
//                this.flag = true;
//            }else{
//                if (kafkaMessageList.size()<=10){
//                    kafkaMessageList.add(kafkaMessage);
//                    if (kafkaMessageList.size()==10){
//                        this.flag = true;
//                    }
//                }
//            }
//
//        }
//        if (flag){
//            if (kafkaMessageList !=null && kafkaMessageList.size()>0){
//                // 然后调用dola的接口并接收，针对接收对象进行解析传回到前端展示
//                String jsonResult = dolaApi.parseMsgList(JSONObject.toJSONString(kafkaMessageList));
//                logger.info(jsonResult+"解析结果预览"+"---"+flag);
//                //返回给前端后直接处理
//                WebSocketServer.sendRealTimeData(jsonResult, amProtocol.getMac());
//                //清理掉集合数据
//                kafkaMessageList.clear();
//                if (closeFlag){
//                    this.flag = false;
//                }
//            }
//        }
//
//    }

    public static void setWsFlag(boolean flag1) {
        flag = flag1;
    }
    public static void setCloseFlag(boolean flag2) {
        closeFlag = flag2;
    }
    public static void setWsState(int state) {
        states = state;
     }
    public static void setMac(String mac) {
        sbMac = mac;
    }
    public static void setType(int type1) {
        type = type1;
    }

    private static String bytesToTenString(byte[] byteArr) {
        StringBuilder sb = new StringBuilder(byteArr.length);
        String sTemp;
        for (byte b : byteArr) {
            sTemp = Integer.toHexString(0xFF & b);
            long decNum = Long.parseLong(sTemp, 16);
            sb.append(decNum).append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }


//    public void parseSerilView(KafkaMessage kafkaMessage, String serialPort) {
//        kafkaMessage.setType(ClientListernerUtil.type);
//        logger.info(kafkaMessage+"解析预览"+"---"+flag);
//        //解析预览这边建list对象取10条kafkaMessage对象加入到集合对象
//        if (flag == false && serialPort.equalsIgnoreCase(sbMac)){
//            if (kafkaMessageList.size()<=10){
//                kafkaMessageList.add(kafkaMessage);
//                if (kafkaMessageList.size()==10){
//                    this.flag = true;
//                }
//            }
//        }
//        if (flag){
//            if (kafkaMessageList !=null && kafkaMessageList.size()>0){
//                // 然后调用dola的接口并接收，针对接收对象进行解析传回到前端展示
//                String jsonResult = dolaApi.parseMsgList(JSONObject.toJSONString(kafkaMessageList));
//                logger.info(jsonResult+"解析结果预览"+"---"+flag);
//                //返回给前端后直接处理
//                WebSocketServer.sendRealTimeData(jsonResult, serialPort);
//                //清理掉集合数据
//                kafkaMessageList.clear();
//                if (closeFlag){
//                    this.flag = false;
//                }
//
//            }
//        }
//    }
//
//    /**
//     * 同步原始数据到岸端
//     * @param amProtocol
//     */
//    public void syncAllRawData(AmProtocol amProtocol) {
//        //当岸端有开启实时预览时才做处理
//        if(redisCache.getCacheObject("RT_DB_"+amProtocol.getMac())!=null){
//            StoreService storeService = SpringUtils.getBean(StoreService.class);
//            if (DeviceTypeEnum.GPS_IN.getValue().equals(ClientListernerUtil.type) || DeviceTypeEnum.COMPASS_IN.getValue().equals(ClientListernerUtil.type)){
//                String messages = bytesTo16String(amProtocol.getContent());
//                AcquisitionMessage acquisitionMessage = new AcquisitionMessage();
//                acquisitionMessage.setContent(messages);
//                acquisitionMessage.setMac(amProtocol.getMac());
//                KafkaMessage kafkaMessage = new KafkaMessage("raws", JSONObject.toJSONString(acquisitionMessage), 30, System.currentTimeMillis());
//                storeService.sendRawData2Kafka(kafkaMessage);
//            }else{
//                String messages = new String(amProtocol.getContent(), StandardCharsets.UTF_8);
//                AcquisitionMessage acquisitionMessage = new AcquisitionMessage();
//                acquisitionMessage.setContent(messages);
//                acquisitionMessage.setMac(amProtocol.getMac());
//                KafkaMessage kafkaMessage = new KafkaMessage("raws", JSONObject.toJSONString(acquisitionMessage), 30, System.currentTimeMillis());
//                storeService.sendRawData2Kafka(kafkaMessage);
//            }
//
//        }
//
//    }

    private static String bytesTo16String(byte[] src){
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString().toUpperCase();

    }

    public static void main(String[] args) throws ParseException {

//        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm",new Date());
//        long ct = DateUtils.parseDate(dateStr,"yyyy-MM-dd HH:mm").getTime();
//        String s = "CJ_KZ_"+ct;
//        System.out.println(s.split("_")[2]);
//        Date date = new Date();
//        long l = Long.parseLong("1635327960000");
//        date.setTime(l);
//        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm",new Date());
//        long ct = DateUtils.parseDate(dateStr,"yyyy-MM-dd HH:mm").getTime();
//        Date date1 = DateUtils.parseDate(dateStr,"yyyy-MM-dd HH:mm");
//        Calendar calendar1 = Calendar.getInstance();
//        calendar1.setTime(date1);
//        calendar1.add(Calendar.MINUTE, -1);
//        System.out.println(calendar1.getTime().getTime());
//        long ct = DateUtils.parseDate(dateStr,"yyyy-MM-dd HH:mm").getTime();
//        Date date1 = new Date();
//        date1.setTime(Long.parseLong(s.split("_")[2]));
//        long between = (date1.getTime() - date.getTime()) / 1000;
//        long min = between % (24 * 3600) % 3600 / 60;
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(date);
//        calendar.add(Calendar.MINUTE, 1);
//        while (calendar.getTime().before(calendar1.getTime())){
//
//
//            System.out.println(calendar.getTime());
//            calendar.add(Calendar.MINUTE, 1);
//
//        }
//        Calendar calendar22 = Calendar.getInstance();
//        calendar22.setTimeInMillis(l);
//        System.out.println("222222"+calendar22.getTime()+"333"+calendar1.getTime());
//        System.out.println(min);
        String str = "&&&000#SoOLH#34EAE7703E34#12345678901234567890@@&&&000#SoOLH#34EAE7703E34#12345678901234567890@@$GPVTG,63.1,T,,M,0.87,N,1.61,K,P*21";
        int i = str.lastIndexOf("@@")-46;
        System.out.println(i);
    }

    /**
     * 岸端操作过来执行机器重启
     * @throws IOException
     */
    public static void reboot() throws Exception {
        Process process = Runtime.getRuntime().exec("reboot");
        logger.info("重启语句执行完毕");
    }
}
