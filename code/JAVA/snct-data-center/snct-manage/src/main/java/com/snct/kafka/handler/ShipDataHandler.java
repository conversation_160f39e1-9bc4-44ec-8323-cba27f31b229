package com.snct.kafka.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.snct.common.core.domain.entity.SysDept;
import com.snct.kafka.KafkaMessage;
import com.snct.system.domain.Ship;
import com.snct.system.service.ISysDeptService;
import com.snct.system.service.IShipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 船舶数据处理器
 *
 * <AUTHOR>
 */
@Component
public class ShipDataHandler extends AbstractDeviceHandler {

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private IShipService shipService;

    @Override
    protected boolean saveToMysql(JSONObject jsonObject, String sn, Map<String, Long> deviceInfo) {
        try {
            Ship ship = JSON.parseObject(jsonObject.toJSONString(), Ship.class);
            if (ship == null) {
                logger.error("解析船舶数据失败");
                return false;
            }

            // 通过SN判断船舶是否已存在
            if (StringUtils.isEmpty(ship.getSn())) {
                logger.error("船舶数据缺少SN标识");
                return false;
            }

            String deptCode = jsonObject.getString("deptCode");

            // 根据deptCode查询部门信息
            if (!StringUtils.isEmpty(deptCode)) {
                List<SysDept> depts = sysDeptService.selectDeptByDeptCode(deptCode);
                if (depts != null && !depts.isEmpty()) {
                    SysDept dept = depts.get(0);
                    ship.setDeptId(dept.getDeptId());
                    logger.info("根据deptCode [{}] 获取到部门ID: {}", deptCode, dept.getDeptId());
                } else {
                    logger.warn("未找到deptCode [{}] 对应的部门", deptCode);
                }
            }

            Ship queryShip = new Ship();
            queryShip.setSn(ship.getSn());
            List<Ship> existingShips = shipService.selectShipList(queryShip);
            if (existingShips != null && !existingShips.isEmpty()) {
                logger.info("船舶数据已存在，SN: {}", ship.getSn());
                return true; // 已存在视为处理成功
            }

            Long daShipId = ship.getShipId();
            ship.setDaShipId(daShipId);

            // 添加新船舶
            int result = shipService.insertShip(ship);
            if (result > 0) {
                logger.info("成功添加新船舶，名称: {}, SN: {}, DA_SHIP_ID: {}, DeptId: {}",
                        ship.getName(), ship.getSn(), daShipId, ship.getDeptId());
                return true;
            } else {
                logger.error("添加船舶失败");
                return false;
            }
        } catch (Exception e) {
            logger.error("处理船舶数据异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    protected boolean saveToHbase(KafkaMessage kafkaMessage) {
        // 船舶同步数据通常不需要保存到HBase
        // 这里返回true表示处理成功
        logger.info("船舶数据不需要保存到HBase");
        return true;
    }
} 