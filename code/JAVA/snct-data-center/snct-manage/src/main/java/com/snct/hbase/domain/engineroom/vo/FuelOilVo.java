package com.snct.hbase.domain.engineroom.vo;

import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;
import com.snct.hbase.domain.engineroom.EngineroomData;
import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * 燃油系统数据
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:fuel_oil")
public class FuelOilVo {

    @HBaseColumn(family = "rowkey",qualifier = "rowkey")
    String id;

    private Long time;

    private String bjTime;

    /**
     *应急发电机柴油柜油位 14011
     */
    @Excel(name="应急发电机柴油柜油位")
    @HBaseColumn(family = "i",qualifier = "egtkl")
    private String egTkLevel;

    /**
     * 焚烧炉油柜油位 14012
     */
    @Excel(name="焚烧炉油柜油位")
    @HBaseColumn(family = "i",qualifier = "idtkl")
    private String incineratorDoTkLevel;

    /**
     * 1号柴油驳运泵出口压力 13005-1
     */
    @Excel(name = "1号柴油驳运泵出口压力")
    @HBaseColumn(family = "i",qualifier = "no1tp")
    private String no1TransferPress;

    /**
     * 2号柴油驳运泵出口压力 13010-1
     */
    @Excel(name = "2号柴油驳运泵出口压力")
    @HBaseColumn(family = "i",qualifier = "no2tp")
    private String no2TransferPress;

    /**
     * 1号柴油日用柜（左）油位 14009
     */
    @Excel(name = "1号柴油日用柜（左）油位")
    @HBaseColumn(family = "i",qualifier = "no1stkl")
    private String no1ServiceTkLevel;

    /**
     * 2号柴油日用柜（右）油位 14010
     */
    @Excel(name = "1号柴油日用柜（右）油位")
    @HBaseColumn(family = "i",qualifier = "no2stkl")
    private String no2ServiceTkLevel;

    /**
     * 5号柴油柜（中）油位 14007
     */
    @Excel(name = "5号柴油柜（中）")
    @HBaseColumn(family = "i",qualifier = "no5tkl")
    private String no5TkLevel;

    /**
     * 4号柴油柜（中）油位 14006
     */
    @Excel(name = "4号柴油柜（中）")
    @HBaseColumn(family = "i",qualifier = "no4tkl")
    private String no4TkLevel;

    /**
     * 3号柴油柜（右）油位 14005
     */
    @Excel(name = "3号柴油柜（右）")
    @HBaseColumn(family = "i",qualifier = "no3tkl")
    private String no3TkLevel;

    /**
     * 低硫柴油柜（左）油位 14004
     */
    @Excel(name = "低硫柴油柜（左）油位")
    @HBaseColumn(family = "i",qualifier = "lstkl")
    private String lsTkLevel;

    /**
     * 1号柴油柜（中）油位 14001
     */
    @Excel(name = "1号柴油柜（中）油位")
    @HBaseColumn(family = "i",qualifier = "no1tkl")
    private String no1TkLevel;

    /**
     * 2号柴油柜（左）油位 14002
     */
    @Excel(name = "2号柴油柜（左）油位")
    @HBaseColumn(family = "i",qualifier = "no2tklp")
    private String no2TkLevelP;

    /**
     * 2号柴油柜（右）油位 14003
     */
    @Excel(name = "2号柴油柜（右）油位 ")
    @HBaseColumn(family = "i",qualifier = "no2tkls")
    private String no2TkLevelS;

    /**
     *  燃油溢油柜油位 14008
     */
    @Excel(name = "燃油溢油柜油位 ")
    @HBaseColumn(family = "i",qualifier = "fotkl")
    private String foOverFlowTkLevel;

    public FuelOilVo(){

    }
    public FuelOilVo(Map<String, EngineroomData> map){
        
        this.egTkLevel = AnalysisUtils.analysis(map.get("14011"));
        this.incineratorDoTkLevel = AnalysisUtils.analysis(map.get("14012"));
        this.no1TransferPress = AnalysisUtils.analysis(map.get("13005-1"));
        this.no2TransferPress = AnalysisUtils.analysis(map.get("13010-1"));
        this.no1ServiceTkLevel = AnalysisUtils.analysis(map.get("14009"));
        this.no2ServiceTkLevel = AnalysisUtils.analysis(map.get("14010"));
        this.no5TkLevel = AnalysisUtils.analysis(map.get("14007"));
        this.no4TkLevel = AnalysisUtils.analysis(map.get("14006"));
        this.no3TkLevel = AnalysisUtils.analysis(map.get("14005"));
        this.lsTkLevel = AnalysisUtils.analysis(map.get("14004"));
        this.no1TkLevel = AnalysisUtils.analysis(map.get("14001"));
        this.no2TkLevelP = AnalysisUtils.analysis(map.get("14002"));
        this.no2TkLevelS = AnalysisUtils.analysis(map.get("14003"));
        this.foOverFlowTkLevel = AnalysisUtils.analysis(map.get("14008"));
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return bjTime;
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getEgTkLevel() {
        return egTkLevel;
    }

    public void setEgTkLevel(String egTkLevel) {
        this.egTkLevel = egTkLevel;
    }

    public String getIncineratorDoTkLevel() {
        return incineratorDoTkLevel;
    }

    public void setIncineratorDoTkLevel(String incineratorDoTkLevel) {
        this.incineratorDoTkLevel = incineratorDoTkLevel;
    }

    public String getNo1TransferPress() {
        return no1TransferPress;
    }

    public void setNo1TransferPress(String no1TransferPress) {
        this.no1TransferPress = no1TransferPress;
    }

    public String getNo2TransferPress() {
        return no2TransferPress;
    }

    public void setNo2TransferPress(String no2TransferPress) {
        this.no2TransferPress = no2TransferPress;
    }

    public String getNo1ServiceTkLevel() {
        return no1ServiceTkLevel;
    }

    public void setNo1ServiceTkLevel(String no1ServiceTkLevel) {
        this.no1ServiceTkLevel = no1ServiceTkLevel;
    }

    public String getNo2ServiceTkLevel() {
        return no2ServiceTkLevel;
    }

    public void setNo2ServiceTkLevel(String no2ServiceTkLevel) {
        this.no2ServiceTkLevel = no2ServiceTkLevel;
    }

    public String getNo5TkLevel() {
        return no5TkLevel;
    }

    public void setNo5TkLevel(String no5TkLevel) {
        this.no5TkLevel = no5TkLevel;
    }

    public String getNo4TkLevel() {
        return no4TkLevel;
    }

    public void setNo4TkLevel(String no4TkLevel) {
        this.no4TkLevel = no4TkLevel;
    }

    public String getNo3TkLevel() {
        return no3TkLevel;
    }

    public void setNo3TkLevel(String no3TkLevel) {
        this.no3TkLevel = no3TkLevel;
    }

    public String getLsTkLevel() {
        return lsTkLevel;
    }

    public void setLsTkLevel(String lsTkLevel) {
        this.lsTkLevel = lsTkLevel;
    }

    public String getNo1TkLevel() {
        return no1TkLevel;
    }

    public void setNo1TkLevel(String no1TkLevel) {
        this.no1TkLevel = no1TkLevel;
    }

    public String getNo2TkLevelP() {
        return no2TkLevelP;
    }

    public void setNo2TkLevelP(String no2TkLevelP) {
        this.no2TkLevelP = no2TkLevelP;
    }

    public String getNo2TkLevelS() {
        return no2TkLevelS;
    }

    public void setNo2TkLevelS(String no2TkLevelS) {
        this.no2TkLevelS = no2TkLevelS;
    }

    public String getFoOverFlowTkLevel() {
        return foOverFlowTkLevel;
    }

    public void setFoOverFlowTkLevel(String foOverFlowTkLevel) {
        this.foOverFlowTkLevel = foOverFlowTkLevel;
    }
}
