package com.snct.hbase.domain.snapshot;

import com.snct.hbase.domain.HbaseBaseEntity;

/**
 * 快照记录
 *
 * <AUTHOR>
 */
public class SnapshotLogEntityHbase extends HbaseBaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 部门ID（岸上需要）
     */
    private Long deptId;

    private String sn;

    private String shipName;

    /**
     * 通道编号
     */
    private String channelCode;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * 截屏时间
     */
    private Long operateTime;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 目录
     */
    private String directory;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public Long getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Long operateTime) {
        this.operateTime = operateTime;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getDirectory() {
        return directory;
    }

    public void setDirectory(String directory) {
        this.directory = directory;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }
}
