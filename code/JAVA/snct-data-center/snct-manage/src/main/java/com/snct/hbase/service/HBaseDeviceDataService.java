package com.snct.hbase.service;

import com.snct.hbase.utils.HBaseDaoUtil;
import com.snct.hbase.domain.hbase.PduHbaseVo;
import com.snct.hbase.utils.HConnectionFactory;
import org.apache.hadoop.hbase.filter.FilterList;
import org.apache.hadoop.hbase.filter.PrefixFilter;
import org.apache.hadoop.hbase.filter.SingleColumnValueFilter;
import org.apache.hadoop.hbase.filter.CompareFilter;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.TableName;

import java.util.*;
import java.io.IOException;

/**
 * HBase设备数据通用服务
 * 提供各类设备数据的通用查询功能
 *
 * <AUTHOR>
 */
@Service
public class HBaseDeviceDataService {
    /**
     * 日志记录器
     */
    private static final Logger logger = LoggerFactory.getLogger(HBaseDeviceDataService.class);

    /**
     * HBase 命名空间
     */
    private static final String NAMESPACE = "snct";

    /**
     * 时间列族和列名
     */
    private static final String FAMILY = "i";
    private static final String TIME_QUALIFIER = "i_b_t";

    /**
     * HBase 数据访问工具类
     */
    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    @Autowired
    private PageUtils pageUtils;

    /**
     * 分页查询设备数据
     * 按照清晰的步骤处理查询逻辑
     *
     * @param deviceType    设备类型
     * @param deptId        部门ID
     * @param sn            船舶SN
     * @param deviceId      设备ID
     * @param pageNum       页码
     * @param pageSize      每页大小
     * @param startDateTime 开始日期时间
     * @param endDateTime   结束日期时间
     * @param isAdmin       是否管理员
     * @param voClass       返回对象类型
     * @return 分页结果
     */
    public <T> Map<String, Object> queryRowList(
            String deviceType, Long deptId, String sn, Long deviceId, int pageNum, int pageSize, String sortOrder,
            String startDateTime, String endDateTime,
            boolean isAdmin, Class<T> voClass) {

        try {
            // 1、准备查询参数
            QueryParams queryParams = prepareQueryParams(
                    deviceType, deptId, sn, deviceId, startDateTime, endDateTime, isAdmin, sortOrder, voClass);
            
            // 2、获取RowKey列表
            List<String> rowKeyList = getRowKeyList(queryParams);
            if (rowKeyList == null || rowKeyList.isEmpty()) {
                // 如果没有数据，返回空结果
                return createEmptyResult();
            }
            
            // 3、分页处理
            Map<String, String> pageInfo = pageUtils.getSERowKey(rowKeyList, pageNum, pageSize,  sortOrder);

            // 4、从HBase获取详细数据
            List<T> detailDataList = getDetailData(queryParams, pageInfo);
            
            // 5、组装结果
            return createResult(detailDataList, rowKeyList.size(), queryParams.isDesc());
            
        } catch (Exception e) {
            logger.error("分页查询发生异常: {}", e.getMessage(), e);
            return createEmptyResult();
        }
    }

    /**
     * 准备查询参数
     */
    private <T> QueryParams prepareQueryParams(
            String deviceType, Long deptId, String sn, Long deviceId,
            String startDateTime, String endDateTime, boolean isAdmin, String sortOrder,Class<T> voClass) {
        
        QueryParams params = new QueryParams();
        
        // 基本参数设置
        params.setTableName(getTableName(deviceType));
        params.setDeptId(deptId);
        params.setSn(sn);
        params.setDeviceId(deviceId);
        params.setStartDateTime(startDateTime);
        params.setEndDateTime(endDateTime);
        params.setIsAdmin(isAdmin);

        // 是否倒序
        params.setIsDesc("desc".equalsIgnoreCase(sortOrder));
        
        // 判断查询条件类型
        params.setHasTimeRange((startDateTime != null && !startDateTime.isEmpty())
                || (endDateTime != null && !endDateTime.isEmpty()));
                
        params.setCanUseRowKeyTime(canUseRowKeyTimeFilter(deptId, sn, deviceId));
        
        // 处理时间格式
        if (params.hasTimeRange()) {
            params.setStartDate(formatDateTimeForRowKey(startDateTime));
            params.setEndDate(formatDateTimeForRowKey(endDateTime));
        }
        
        // 创建VO对象
        try {
            params.setVo(voClass.newInstance());
        } catch (Exception e) {
            logger.error("创建VO对象实例失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建查询对象失败: " + e.getMessage());
        }
        
        return params;
    }
    
    /**
     * 格式化时间字符串为RowKey格式
     */
    private String formatDateTimeForRowKey(String dateTime) {
        if (dateTime == null || dateTime.isEmpty()) {
            return null;
        }
        
        String dateTimeStr = dateTime.replaceAll("[^0-9]", "");
        if (dateTimeStr.length() >= 14) {
            // 提取日期和时间部分
            String dateStr = dateTimeStr.substring(0, 8);
            String timeStr = dateTimeStr.substring(8, 14);
            return dateStr + "_" + timeStr;
        } else if (dateTimeStr.length() >= 8) {
            // 只有日期部分
            return dateTimeStr.substring(0, 8);
        }
        
        return null;
    }
    
    /**
     * 获取RowKey列表
     */
    private <T> List<String> getRowKeyList(QueryParams params) {
        if (params.hasTimeRange() && params.canUseRowKeyTime()) {
            // 基于RowKey的时间过滤
            String startRowKey = buildStartRowKey(
                    params.getDeptId(), params.getSn(), params.getDeviceId(), 
                    params.getStartDate(), params.isAdmin());
                    
            String endRowKey = buildEndRowKey(
                    params.getDeptId(), params.getSn(), params.getDeviceId(), 
                    params.getEndDate(), params.isAdmin());
            
            logger.debug("使用基于RowKey的时间过滤: 表名={}, startRowKey={}, endRowKey={}",
                    params.getTableName(), startRowKey, endRowKey);
            
            return hBaseDaoUtil.getRowKeyListWithFirstKeyOnlyFilter(
                    params.getVo(), params.getTableName(), startRowKey, endRowKey,  null, params.isDesc(),5000);
                    
        } else if (params.hasTimeRange()) {
            // 使用复合过滤器
            FilterList filterList = buildFilterList(
                    params.getDeptId(), params.getSn(), params.getDeviceId(),
                    params.getStartDateTime(), params.getEndDateTime(), params.isAdmin());
                    
            return hBaseDaoUtil.getRowKeyListWithFilterList(
                    params.getVo(), params.getTableName(), null, null, filterList, params.isDesc(), 5000, FAMILY, TIME_QUALIFIER);
                    
        } else {
            // 无时间范围条件，使用RowKey范围查询
            String startRowKey = buildStartRowKey(
                    params.getDeptId(), params.getSn(), params.getDeviceId(), null, params.isAdmin());
                    
            String endRowKey = buildEndRowKey(
                    params.getDeptId(), params.getSn(), params.getDeviceId(), null, params.isAdmin());
            
            logger.debug("构建查询范围: 表名={}, startRowKey={}, endRowKey={}",
                    params.getTableName(), startRowKey, endRowKey);
            
            return hBaseDaoUtil.getRowKeyListWithFirstKeyOnlyFilter(
                    params.getVo(), params.getTableName(), startRowKey, endRowKey, null, params.isDesc(), 5000);
        }
    }
    
    /**
     * 获取详细数据
     */
    private <T> List<T> getDetailData(QueryParams params, Map<String, String> pageInfo) {
        // 如果有时间范围
        //if (params.hasTimeRange() && !params.canUseRowKeyTime()) {
        //    // 使用复合过滤器
        //    FilterList filterList = buildFilterList(
        //            params.getDeptId(), params.getSn(), params.getDeviceId(),
        //            params.getStartDateTime(), params.getEndDateTime(), params.isAdmin());
        //
        //    return hBaseDaoUtil.scanWithFilterList(params.getVo(), params.getTableName(),
        //            pageInfo.get("sRowKey"), pageInfo.get("eRowKey"), filterList, Integer.parseInt(pageInfo.get("pageSize")));
        //} else {
        //    // 使用普通分页过滤器
        //    return hBaseDaoUtil.scanWithPageFilter(params.getVo(), params.getTableName(),
        //            pageInfo.get("sRowKey"), pageInfo.get("eRowKey"), Integer.parseInt(pageInfo.get("pageSize")));
        //}
        return hBaseDaoUtil.scanWithPageFilter(params.getVo(), params.getTableName(),
                            pageInfo.get("sRowKey"), pageInfo.get("eRowKey"), Integer.parseInt(pageInfo.get("pageSize")), params.isDesc());
    }
    
    /**
     * 创建空的结果集
     */
    private Map<String, Object> createEmptyResult() {
        Map<String, Object> result = new HashMap<>();
        result.put("data", new ArrayList<>());
        result.put("total", 0);
        return result;
    }
    
    /**
     * 创建查询结果
     */
    private <T> Map<String, Object> createResult(List<T> dataList, int total,  boolean isDesc) {
        Map<String, Object> result = new HashMap<>();
        result.put("data", dataList);
        result.put("total", total);
        result.put("isDesc", isDesc);
        return result;
    }

    /**
     * 判断是否可以使用基于RowKey的时间过滤
     * 当部门ID、船舶SN和设备ID都不为空时，可以使用
     *
     * @param deptId   部门ID
     * @param sn       船舶SN
     * @param deviceId 设备ID
     * @return 是否可以使用基于RowKey的时间过滤
     */
    private boolean canUseRowKeyTimeFilter(Long deptId, String sn, Long deviceId) {
        return deptId != null && sn != null && !sn.isEmpty() && deviceId != null;
    }

    /**
     * 构建复合过滤器
     *
     * @param deptId        部门ID
     * @param sn            船舶SN
     * @param deviceId      设备ID
     * @param startDateTime 开始日期时间
     * @param endDateTime   结束日期时间
     * @param isAdmin       是否管理员
     * @return 过滤器列表
     */
    private FilterList buildFilterList(Long deptId, String sn, Long deviceId,
                                       String startDateTime, String endDateTime,
                                       boolean isAdmin) {
        FilterList filterList = new FilterList(FilterList.Operator.MUST_PASS_ALL);

        // 添加前缀过滤器
        String prefix = buildPrefixKey(deptId, sn, deviceId, isAdmin);
        if (prefix != null && !prefix.isEmpty()) {
            filterList.addFilter(new PrefixFilter(Bytes.toBytes(prefix)));
            logger.debug("添加前缀过滤器: {}", prefix);
        }

        // 只有在不能使用基于RowKey的时间过滤时，才添加时间范围过滤器
        if (!canUseRowKeyTimeFilter(deptId, sn, deviceId)) {
            // 添加时间范围过滤器
            if (startDateTime != null && !startDateTime.isEmpty()) {
                SingleColumnValueFilter startTimeFilter = new SingleColumnValueFilter(
                        Bytes.toBytes(FAMILY),
                        Bytes.toBytes(TIME_QUALIFIER),
                        CompareFilter.CompareOp.GREATER_OR_EQUAL,
                        Bytes.toBytes(startDateTime)
                );
                startTimeFilter.setFilterIfMissing(true);
                filterList.addFilter(startTimeFilter);
                logger.debug("添加开始时间过滤器: >= {}", startDateTime);
            }

            if (endDateTime != null && !endDateTime.isEmpty()) {
                SingleColumnValueFilter endTimeFilter = new SingleColumnValueFilter(
                        Bytes.toBytes(FAMILY),
                        Bytes.toBytes(TIME_QUALIFIER),
                        CompareFilter.CompareOp.LESS_OR_EQUAL,
                        Bytes.toBytes(endDateTime)
                );
                endTimeFilter.setFilterIfMissing(true);
                filterList.addFilter(endTimeFilter);
                logger.debug("添加结束时间过滤器: <= {}", endDateTime);
            }
        }

        return filterList;
    }

    /**
     * 构建用于前缀过滤的key
     *
     * @param deptId   部门ID
     * @param sn       船舶SN
     * @param deviceId 设备ID
     * @param isAdmin  是否管理员
     * @return 前缀键
     */
    private String buildPrefixKey(Long deptId, String sn, Long deviceId, boolean isAdmin) {
        StringBuilder prefix = new StringBuilder();

        // 未指定部门ID且非管理员，无法构建有效前缀
        if (deptId == null && !isAdmin) {
            return "";
        }

        // 指定了部门ID
        if (deptId != null) {
            prefix.append(String.format("%05d", deptId));

            // 如果还指定了船舶SN
            if (sn != null && !sn.isEmpty()) {
                prefix.append("_").append(sn);

                // 如果还指定了设备ID
                if (deviceId != null) {
                    prefix.append("_").append(String.format("%05d", deviceId));
                }
            }
        }

        return prefix.toString();
    }

    /**
     * 构建起始RowKey
     * 根据查询设计文档实现各种条件查询
     *
     * @param deptId    部门ID
     * @param sn        船舶SN
     * @param deviceId  设备ID
     * @param startDate 开始日期
     * @param isAdmin   是否为管理员
     * @return 起始RowKey
     */
    private String buildStartRowKey(Long deptId, String sn, Long deviceId, String startDate, boolean isAdmin) {
        StringBuilder builder = new StringBuilder();

        // 管理员可以查询所有数据，非管理员必须指定部门
        if (isAdmin && deptId == null) {
            // 管理员查询所有部门的情况
            builder.append("00000");
        } else {
            // 必须有部门ID
            Long effectiveDeptId = (deptId != null) ? deptId : 0L;
            builder.append(String.format("%05d", effectiveDeptId));
        }

        // 添加船舶SN
        builder.append("_");
        if (sn != null && !sn.isEmpty()) {
            builder.append(sn);
        } else {
            // 未指定船舶SN，使用起始值
            builder.append("000000");
        }

        // 添加设备ID
        builder.append("_");
        if (deviceId != null) {
            builder.append(String.format("%05d", deviceId));
        } else {
            // 未指定设备ID，使用起始值
            builder.append("00000");
        }

        // 添加时间范围
        builder.append("_");
        if (startDate != null && !startDate.isEmpty()) {
            // 检查startDate是否已经包含时间部分
            if (startDate.contains("_")) {
                // 已经包含日期和时间部分，直接使用
                builder.append(startDate);
            } else {
                // 只包含日期部分，添加默认的起始时间
                builder.append(startDate).append("_000000");
            }
        } else {
            // 未指定开始日期，从最早的数据开始
            builder.append("00000000_000000");
        }

        return builder.toString();
    }

    /**
     * 构建结束RowKey
     * 根据查询设计文档实现各种条件查询
     *
     * @param deptId   部门ID
     * @param sn       船舶SN
     * @param deviceId 设备ID
     * @param endDate  结束日期
     * @param isAdmin  是否为管理员
     * @return 结束RowKey
     */
    private String buildEndRowKey(Long deptId, String sn, Long deviceId, String endDate, boolean isAdmin) {
        StringBuilder builder = new StringBuilder();

        // 管理员可以查询所有数据，非管理员必须指定部门
        if (isAdmin && deptId == null) {
            // 管理员查询所有部门的情况
            builder.append("99999");
        } else {
            // 必须有部门ID 
            Long effectiveDeptId = (deptId != null) ? deptId : 0L;
            builder.append(String.format("%05d", effectiveDeptId));
        }

        // 添加船舶SN
        builder.append("_");
        if (sn != null && !sn.isEmpty()) {
            builder.append(sn);
        } else {
            // 未指定船舶SN，使用结束值
            builder.append("zzzzzz");
        }

        // 添加设备ID
        builder.append("_");
        if (deviceId != null) {
            builder.append(String.format("%05d", deviceId));
        } else {
            // 未指定设备ID，使用结束值
            builder.append("99999");
        }

        // 添加时间范围
        builder.append("_");
        if (endDate != null && !endDate.isEmpty()) {
            // 检查endDate是否已经包含时间部分
            if (endDate.contains("_")) {
                // 已经包含日期和时间部分，直接使用
                builder.append(endDate);
            } else {
                // 只包含日期部分，添加默认的结束时间
                builder.append(endDate).append("_235959");
            }
        } else {
            // 未指定结束日期，到最新的数据为止
            builder.append("99999999_999999");
        }

        return builder.toString();
    }

    /**
     * 获取单条设备数据详情
     *
     * @param deviceType 设备类型 (modem, amplifier, pdu, pdu_out)
     * @param rowKey     数据行键
     * @param voClass    返回对象的类，例如ModemHbaseVo.class
     * @return 设备数据详情
     */
    public <T> T getDeviceDetail(String deviceType, String rowKey, Class<T> voClass) {
        try {
            // 获取表名
            String tableName = getTableName(deviceType);

            // 创建一个空的VO对象用于查询
            T vo = voClass.newInstance();

            // 使用HBaseDaoUtil的get方法查询单条数据
            List<T> resultList = hBaseDaoUtil.get(vo, rowKey);

            if (resultList != null && !resultList.isEmpty()) {
                return resultList.get(0);
            }

            return null;
        } catch (Exception e) {
            logger.error("获取设备数据详情时发生异常: 设备类型={}, rowKey={}, 错误={}",
                    deviceType, rowKey, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据设备类型获取表名
     *
     * @param deviceType 设备类型
     * @return 表名
     */
    private String getTableName(String deviceType) {
        if (deviceType == null || deviceType.isEmpty()) {
            throw new IllegalArgumentException("设备类型不能为空");
        }

        return NAMESPACE + ":" + deviceType;
    }


    /**
     * 获取设备通道数据
     *
     * @param deviceType 设备类型
     * @param rowKey 数据行键
     * @return 通道数据映射，键为通道索引，值为通道数据对象
     */
    public Map<String, Object> getDeviceChannelData(String deviceType, String rowKey) {
        if (!"pdu".equals(deviceType)) {
            logger.warn("获取通道数据仅支持PDU设备，当前设备类型: {}", deviceType);
            return new HashMap<>();
        }

        Map<String, Object> channelDataMap = new HashMap<>();

        try {
            // 获取表名
            String tableName = getTableName(deviceType);

            // 获取PDU对象详情
            PduHbaseVo pduVo = getDeviceDetail(deviceType, rowKey, PduHbaseVo.class);
            if (pduVo == null) {
                logger.warn("未找到指定的PDU设备数据: rowKey={}", rowKey);
                return channelDataMap;
            }

            // 获取通道数据
            Map<Long, PduHbaseVo.ChannelData> channelDataObj = pduVo.getChannelDataMap();
            if (channelDataObj != null && !channelDataObj.isEmpty()) {
                // 转换通道数据格式
                for (Map.Entry<Long, PduHbaseVo.ChannelData> entry : channelDataObj.entrySet()) {
                    Long channelIndex = entry.getKey();
                    PduHbaseVo.ChannelData channelData = entry.getValue();

                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("outIndex", channelData.getOutIndex());
                    dataMap.put("electric", channelData.getElectric());
                    dataMap.put("power", channelData.getPower());
                    dataMap.put("outStatus", channelData.getOutStatus());

                    channelDataMap.put(channelIndex.toString(), dataMap);
                }

                return channelDataMap;
            }

            // 获取表的所有列簇
            List<String> families = hBaseDaoUtil.familys(tableName);

            // 从HBase直接获取通道数据
            Table table = null;
            try {
                table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));
                Get get = new Get(Bytes.toBytes(rowKey));

                // 只获取通道列簇
                for (String family : families) {
                    if (family.startsWith("o")) {
                        get.addFamily(Bytes.toBytes(family));
                    }
                }

                Result result = table.get(get);
                if (!result.isEmpty()) {
                    // 处理获取的通道数据
                    for (String family : families) {
                        if (family.startsWith("o") && family.length() <= 3) {
                            try {
                                int channelIndex = Integer.parseInt(family.substring(1));

                                Map<String, Object> dataMap = new HashMap<>();
                                dataMap.put("outIndex", (long) channelIndex);

                                // 获取电流值
                                byte[] electricBytes = result.getValue(Bytes.toBytes(family), Bytes.toBytes("e_t"));
                                if (electricBytes != null) {
                                    dataMap.put("electric", Double.parseDouble(Bytes.toString(electricBytes)));
                                } else {
                                    dataMap.put("electric", 0.0);
                                }

                                // 获取功率值
                                byte[] powerBytes = result.getValue(Bytes.toBytes(family), Bytes.toBytes("p_w"));
                                if (powerBytes != null) {
                                    dataMap.put("power", Double.parseDouble(Bytes.toString(powerBytes)));
                                } else {
                                    dataMap.put("power", 0.0);
                                }

                                // 获取插座状态
                                byte[] statusBytes = result.getValue(Bytes.toBytes(family), Bytes.toBytes("o_s"));
                                if (statusBytes != null) {
                                    dataMap.put("outStatus", Long.parseLong(Bytes.toString(statusBytes)));
                                } else {
                                    dataMap.put("outStatus", 0L);
                                }

                                channelDataMap.put(String.valueOf(channelIndex), dataMap);
                            } catch (NumberFormatException e) {
                                logger.warn("处理通道索引发生异常: {}, 列簇={}", e.getMessage(), family);
                            }
                        }
                    }

                    if (!channelDataMap.isEmpty()) {
                        return channelDataMap;
                    }
                }
            } finally {
                if (table != null) {
                    try {
                        table.close();
                    } catch (IOException e) {
                        logger.error("关闭HBase表连接时出错: {}", e.getMessage(), e);
                    }
                }
            }

        } catch (Exception e) {
            logger.error("获取设备通道数据时发生异常: 设备类型={}, rowKey={}, 错误={}",
                    deviceType, rowKey, e.getMessage(), e);
        }

        return channelDataMap;
    }
} 