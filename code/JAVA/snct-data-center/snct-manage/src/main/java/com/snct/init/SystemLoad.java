package com.snct.init;

import com.snct.common.utils.spring.SpringUtils;
import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.domain.hbase.AmplifierHbaseVo;
import com.snct.hbase.utils.HBaseDaoUtil;
import com.snct.serialport.RtxtService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * class
 *
 * <AUTHOR>
 * @date 2020/3/5 23:55
 */
@Component
@Order(0)
public class SystemLoad implements CommandLineRunner {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private HBaseDaoUtil hbaseDaoUtil;

    @Override
    public void run(String... args) throws Exception {
        //logger.info("系统基础功能初始化开始...");
        //logger.info("系统基础功能初始化结束...");
    }
}
