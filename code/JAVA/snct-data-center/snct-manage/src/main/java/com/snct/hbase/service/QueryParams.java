package com.snct.hbase.service;

class QueryParams {
    private String tableName;
    private Long deptId;
    private String sn;
    private Long deviceId;
    private String startDateTime;
    private String endDateTime;
    private boolean isDesc;
    private boolean isAdmin;
    private boolean hasTimeRange;
    private boolean canUseRowKeyTime;
    private String startDate;
    private String endDate;
    private Object vo;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public String getStartDateTime() {
        return startDateTime;
    }

    public void setStartDateTime(String startDateTime) {
        this.startDateTime = startDateTime;
    }

    public String getEndDateTime() {
        return endDateTime;
    }

    public void setEndDateTime(String endDateTime) {
        this.endDateTime = endDateTime;
    }

    public boolean isDesc() {
        return isDesc;
    }

    public void setIsDesc(boolean isDesc) {
        this.isDesc = isDesc;
    }

    public boolean isAdmin() {
        return isAdmin;
    }

    public void setIsAdmin(boolean isAdmin) {
        this.isAdmin = isAdmin;
    }

    public boolean hasTimeRange() {
        return hasTimeRange;
    }

    public void setHasTimeRange(boolean hasTimeRange) {
        this.hasTimeRange = hasTimeRange;
    }

    public boolean canUseRowKeyTime() {
        return canUseRowKeyTime;
    }

    public void setCanUseRowKeyTime(boolean canUseRowKeyTime) {
        this.canUseRowKeyTime = canUseRowKeyTime;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public <T> T getVo() {
        return (T) vo;
    }

    public void setVo(Object vo) {
        this.vo = vo;
    }
}