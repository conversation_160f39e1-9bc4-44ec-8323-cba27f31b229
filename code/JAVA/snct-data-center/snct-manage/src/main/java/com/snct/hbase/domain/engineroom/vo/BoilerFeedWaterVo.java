package com.snct.hbase.domain.engineroom.vo;

import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;
import com.snct.hbase.domain.engineroom.EngineroomData;
import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:boiler_feed_water")
public class BoilerFeedWaterVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    private Long time;

    private String bjTime;

    /**
     * 锅炉最低流压  10013
     */
    @HBaseColumn(family = "i", qualifier = "mf_p")
    private String mfPressure;

    /**
     * 锅炉最低回水温度  10014
     */
    @HBaseColumn(family = "i", qualifier = "mft_t")
    private String mfrTemp;

    /**
     * 锅炉出口温度  10008M
     */
    @HBaseColumn(family = "i", qualifier = "o_t")
    private String outletTemp;

    /**
     * 锅炉回水温度  10009
     */
    @HBaseColumn(family = "i", qualifier = "fr_t")
    private String frTemp;

    public BoilerFeedWaterVo(){}

    public BoilerFeedWaterVo(Map<String, EngineroomData> map){
        this.mfPressure = AnalysisUtils.analysis(map.get("10013"));
        this.mfrTemp = AnalysisUtils.analysis(map.get("10014"));
        this.outletTemp = AnalysisUtils.analysis(map.get("10008"));
        this.frTemp = AnalysisUtils.analysis(map.get("10009")) ;

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return bjTime;
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getMfPressure() {
        return mfPressure;
    }

    public void setMfPressure(String mfPressure) {
        this.mfPressure = mfPressure;
    }

    public String getMfrTemp() {
        return mfrTemp;
    }

    public void setMfrTemp(String mfrTemp) {
        this.mfrTemp = mfrTemp;
    }

    public String getOutletTemp() {
        return outletTemp;
    }

    public void setOutletTemp(String outletTemp) {
        this.outletTemp = outletTemp;
    }

    public String getFrTemp() {
        return frTemp;
    }

    public void setFrTemp(String frTemp) {
        this.frTemp = frTemp;
    }
}
