package com.snct.hbase.domain.engineroom.vo;

import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;
import com.snct.hbase.domain.engineroom.EngineroomData;
import com.snct.hbase.utils.AnalysisUtils;
import com.snct.hbase.utils.RowKeyUtils;

import java.util.Map;

/**
 * 油舱液位数据
 *
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:tank_level")
public class TankLevelVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    private Long time;

    private String bjTime;

    /**
     * 1号柴油柜（中）油位  14001
     */
    @Excel(name = "1号柴油柜（中）油位")
    @HBaseColumn(family = "i", qualifier = "n1tml")
    private String no1TkMLevel;

    /**
     * 2号柴油柜（左）油位  14002
     */
    @Excel(name = "2号柴油柜（左）油位")
    @HBaseColumn(family = "i", qualifier = "n2tpl")
    private String no2TkPLevel;

    /**
     * 2号柴油柜（右）油位  14003
     */
    @Excel(name = "2号柴油柜（右）油位")
    @HBaseColumn(family = "i", qualifier = "n2tsl")
    private String no2TkSLevel;

    /**
     * 低硫柴油柜（左）油位  14004
     */
    @Excel(name = "低硫柴油柜（左）油位")
    @HBaseColumn(family = "i", qualifier = "ltpl")
    private String lsTkPLevel;

    /**
     * 3号柴油柜（右）油位  "14005"	"NO.3 DO TK(S) LEVEL"	"m"	"OK"	1.43
     */
    @Excel(name = "3号柴油柜（右）油位")
    @HBaseColumn(family = "i", qualifier = "n3tsl")
    private String no3TkSLevel;

    /**
     * 4号柴油柜（中）油位  "14006"	"NO.4 DO TK(M) LEVEL"	"m"	"OK"	0.99
     */
    @Excel(name = "4号柴油柜（中）油位")
    @HBaseColumn(family = "i", qualifier = "n4tml")
    private String no4TkMLevel;

    /**
     * 5号柴油柜（中）油位  "14007"	"NO.5 DO TK(M) LEVEL"	"m"	"OK"	4.36
     */
    @Excel(name = "5号柴油柜（中）油位")
    @HBaseColumn(family = "i", qualifier = "n5tml")
    private String no5TkMLevel;

    /**
     * 燃油溢油柜油位  "14008"	"FO OVERFLOW TK LEVEL"	"m"	"OK"	0.29
     */
    @Excel(name = "燃油溢油柜油位")
    @HBaseColumn(family = "i", qualifier = "foftl")
    private String foOverflowTkLevel;

    /**
     * 1号柴油日用柜（左）油位  "14009"	"NO.1 DO SERVICE TK(P) LEVEL"	"m"	"OK"	1.73
     */
    @Excel(name = "1号柴油日用柜（左）油位")
    @HBaseColumn(family = "i", qualifier = "n1stpl")
    private String no1ServiceTkPLevel;

    /**
     * 2号柴油日用柜（右）油位  "14010"	"NO.2 DO SERVICE TK(S) LEVEL"	"m"	"OK"	1.77
     */
    @Excel(name = "2号柴油日用柜（右）油位")
    @HBaseColumn(family = "i", qualifier = "n2stsl")
    private String no2ServiceTkSLevel;

    /**
     * 应急发电机柴油柜油位  "14011"	"EG DO TK LEVEL"	"m"	"OK"	1.26
     */
    @Excel(name = "应急发电机柴油柜油位")
    @HBaseColumn(family = "i", qualifier = "edtl")
    private String egDoTkLevel;

    /**
     * 焚烧炉油柜油位  "14012"	"INCINERATOR DO TK LEVEL"	"m"	"OK"	0.52
     */
    @Excel(name = "焚烧炉油柜油位")
    @HBaseColumn(family = "i", qualifier = "idtl")
    private String incineratorDoTkLevel;

    /**
     * 滑油储存柜（左）油位  "14013"	"LO STORAGE TK(P) LEVEL"	"m"	"OK"	1.39
     */
    @Excel(name = "滑油储存柜（左）油位")
    @HBaseColumn(family = "i", qualifier = "lstpl")
    private String loStorageTkPLevel;

    /**
     * 滑油储存柜（右）油位  "14014"	"LO STORAGE TK(S) LEVEL"	"m"	"OK"	0.71
     */
    @Excel(name = "滑油储存柜（右）油位")
    @HBaseColumn(family = "i", qualifier = "lstsl")
    private String loStorageTkSLevel;

    /**
     * 滑油泄放柜油位  "14015"	"LO DRAIN TK LEVEL"	"m"	"OK"	1.3
     */
    @Excel(name = "滑油泄放柜油位")
    @HBaseColumn(family = "i", qualifier = "ldtl")
    private String loDrainTkLevel;

    /**
     * 油渣柜油位  "14016"	"SLUDGE OIL TK LEVEL"	"m"	"OK"	0.47
     */
    @Excel(name = "油渣柜油位")
    @HBaseColumn(family = "i", qualifier = "sotl")
    private String sludgeOilTkLevel;

    public TankLevelVo() {
    }

    public TankLevelVo(Map<String, EngineroomData> map) {
        this.no1TkMLevel = AnalysisUtils.analysisValue(map.get("14001"));
        this.no2TkPLevel = AnalysisUtils.analysisValue(map.get("14002"));
        this.no2TkSLevel = AnalysisUtils.analysisValue(map.get("14003"));
        this.lsTkPLevel = AnalysisUtils.analysisValue(map.get("14004"));
        this.no3TkSLevel = AnalysisUtils.analysisValue(map.get("14005"));
        this.no4TkMLevel = AnalysisUtils.analysisValue(map.get("14006"));
        this.no5TkMLevel = AnalysisUtils.analysisValue(map.get("14007"));
        this.foOverflowTkLevel = AnalysisUtils.analysisValue(map.get("14008"));
        this.no1ServiceTkPLevel = AnalysisUtils.analysisValue(map.get("14009"));
        this.no2ServiceTkSLevel = AnalysisUtils.analysisValue(map.get("14010"));
        this.egDoTkLevel = AnalysisUtils.analysisValue(map.get("14011"));
        this.incineratorDoTkLevel = AnalysisUtils.analysisValue(map.get("14012"));
        this.loStorageTkPLevel = AnalysisUtils.analysisValue(map.get("14013"));
        this.loStorageTkSLevel = AnalysisUtils.analysisValue(map.get("14014"));
        this.loDrainTkLevel = AnalysisUtils.analysisValue(map.get("14015"));
        this.sludgeOilTkLevel = AnalysisUtils.analysisValue(map.get("14016"));
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return RowKeyUtils.getTimeByRowKey(this.id);
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return RowKeyUtils.getBjTimeByRowKey(this.id);
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getNo1TkMLevel() {
        return no1TkMLevel;
    }

    public void setNo1TkMLevel(String no1TkMLevel) {
        this.no1TkMLevel = no1TkMLevel;
    }

    public String getNo2TkPLevel() {
        return no2TkPLevel;
    }

    public void setNo2TkPLevel(String no2TkPLevel) {
        this.no2TkPLevel = no2TkPLevel;
    }

    public String getNo2TkSLevel() {
        return no2TkSLevel;
    }

    public void setNo2TkSLevel(String no2TkSLevel) {
        this.no2TkSLevel = no2TkSLevel;
    }

    public String getLsTkPLevel() {
        return lsTkPLevel;
    }

    public void setLsTkPLevel(String lsTkPLevel) {
        this.lsTkPLevel = lsTkPLevel;
    }

    public String getNo3TkSLevel() {
        return no3TkSLevel;
    }

    public void setNo3TkSLevel(String no3TkSLevel) {
        this.no3TkSLevel = no3TkSLevel;
    }

    public String getNo4TkMLevel() {
        return no4TkMLevel;
    }

    public void setNo4TkMLevel(String no4TkMLevel) {
        this.no4TkMLevel = no4TkMLevel;
    }

    public String getNo5TkMLevel() {
        return no5TkMLevel;
    }

    public void setNo5TkMLevel(String no5TkMLevel) {
        this.no5TkMLevel = no5TkMLevel;
    }

    public String getFoOverflowTkLevel() {
        return foOverflowTkLevel;
    }

    public void setFoOverflowTkLevel(String foOverflowTkLevel) {
        this.foOverflowTkLevel = foOverflowTkLevel;
    }

    public String getNo1ServiceTkPLevel() {
        return no1ServiceTkPLevel;
    }

    public void setNo1ServiceTkPLevel(String no1ServiceTkPLevel) {
        this.no1ServiceTkPLevel = no1ServiceTkPLevel;
    }

    public String getNo2ServiceTkSLevel() {
        return no2ServiceTkSLevel;
    }

    public void setNo2ServiceTkSLevel(String no2ServiceTkSLevel) {
        this.no2ServiceTkSLevel = no2ServiceTkSLevel;
    }

    public String getEgDoTkLevel() {
        return egDoTkLevel;
    }

    public void setEgDoTkLevel(String egDoTkLevel) {
        this.egDoTkLevel = egDoTkLevel;
    }

    public String getIncineratorDoTkLevel() {
        return incineratorDoTkLevel;
    }

    public void setIncineratorDoTkLevel(String incineratorDoTkLevel) {
        this.incineratorDoTkLevel = incineratorDoTkLevel;
    }

    public String getLoStorageTkPLevel() {
        return loStorageTkPLevel;
    }

    public void setLoStorageTkPLevel(String loStorageTkPLevel) {
        this.loStorageTkPLevel = loStorageTkPLevel;
    }

    public String getLoStorageTkSLevel() {
        return loStorageTkSLevel;
    }

    public void setLoStorageTkSLevel(String loStorageTkSLevel) {
        this.loStorageTkSLevel = loStorageTkSLevel;
    }

    public String getLoDrainTkLevel() {
        return loDrainTkLevel;
    }

    public void setLoDrainTkLevel(String loDrainTkLevel) {
        this.loDrainTkLevel = loDrainTkLevel;
    }

    public String getSludgeOilTkLevel() {
        return sludgeOilTkLevel;
    }

    public void setSludgeOilTkLevel(String sludgeOilTkLevel) {
        this.sludgeOilTkLevel = sludgeOilTkLevel;
    }
}
