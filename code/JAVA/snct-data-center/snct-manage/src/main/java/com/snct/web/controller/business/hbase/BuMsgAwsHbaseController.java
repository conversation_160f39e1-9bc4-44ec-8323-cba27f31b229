package com.snct.web.controller.business.hbase;

import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.domain.entity.SysDept;
import com.snct.common.core.page.TableDataInfo;
import com.snct.common.utils.SecurityUtils;
import com.snct.hbase.domain.hbase.AwsHbaseVo;
import com.snct.hbase.service.HBaseDeviceDataService;
import com.snct.system.mapper.SysDeptMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Aws消息Controller（HBase版本）
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
@Api("Aws消息(hbase)")
@RestController
@RequestMapping("/hbase/devicemsg/aws")
public class BuMsgAwsHbaseController extends BaseController
{
    /**
     * 设备类型常量
     */
    private static final String DEVICE_TYPE = "aws";
    
    @Autowired
    private HBaseDeviceDataService deviceDataService;
    
    @Autowired
    private SysDeptMapper deptMapper;

    /**
     * 分页查询Aws消息列表
     * 
     * @param deptId 部门ID
     * @param sn 船舶SN
     * @param deviceId 设备ID
     * @param startDateTime 开始日期时间
     * @param endDateTime 结束日期时间
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 分页数据
     */
    @ApiOperation("查询Aws消息列表")
    //@PreAuthorize("@ss.hasPermi('devicemsg:aws:list')")
    @GetMapping("/query")
    public TableDataInfo query(
            @ApiParam("部门ID") @RequestParam(required = false) Long deptId,
            @ApiParam("船舶SN") @RequestParam(required = false) String sn,
            @ApiParam("设备ID") @RequestParam(required = false) Long deviceId,
            @ApiParam("开始日期时间(yyyy-MM-dd HH:mm:ss)") @RequestParam(required = false) String startDateTime,
            @ApiParam("结束日期时间(yyyy-MM-dd HH:mm:ss)") @RequestParam(required = false) String endDateTime,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页记录数") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("排序方式") @RequestParam(defaultValue = "desc") String sortOrder) {
        
        startPage();
        
        Long effectiveDeptId = processPermissionAndDeptId(deptId);
        boolean isAdmin = SecurityUtils.isAdmin(SecurityUtils.getUserId());
        
        // 从Hbase查询设备数据
        Map<String, Object> queryResult = deviceDataService.queryRowList(
                DEVICE_TYPE, effectiveDeptId, sn, deviceId, pageNum, pageSize, sortOrder,
                startDateTime, endDateTime, isAdmin, AwsHbaseVo.class);

        return getDataTable(
                (List<?>) queryResult.get("data"),
                Long.parseLong(queryResult.get("total").toString()));
    }

    /**
     * 处理权限和部门ID参数
     * 
     * @param requestDeptId 请求中的部门ID参数
     * @return 经过权限处理后的有效部门ID
     */
    private Long processPermissionAndDeptId(Long requestDeptId) {
        // 获取当前用户信息
        Long currentUserDeptId = getDeptId();
        Long currentUserId = SecurityUtils.getUserId();
        boolean isAdmin = SecurityUtils.isAdmin(currentUserId);

        // 管理员可以查询任意部门数据
        if (isAdmin) {
            return requestDeptId; // 可以为null，表示全部部门
        }

        // 非管理员必须查询自己所在部门或子部门数据
        if (requestDeptId != null && !requestDeptId.equals(currentUserDeptId)) {
            // 检查是否为子部门
            if (isChildDepartment(currentUserDeptId, requestDeptId)) {
                return requestDeptId;
            } else {
                logger.warn("用户无权查询部门ID: {}, 已限制为当前用户部门ID: {}", requestDeptId, currentUserDeptId);
                return currentUserDeptId;
            }
        }

        // 未指定部门或指定的就是当前用户部门
        return currentUserDeptId;
    }

    /**
     * 判断指定部门是否为当前用户部门的子部门
     * 
     * @param parentDeptId 父部门ID
     * @param childDeptId 子部门ID
     * @return 是否为子部门
     */
    private boolean isChildDepartment(Long parentDeptId, Long childDeptId) {
        List<SysDept> childDepts = deptMapper.selectChildrenDeptById(parentDeptId);
        List<Long> childDeptIds = childDepts.stream()
                .map(SysDept::getDeptId)
                .collect(Collectors.toList());
        return childDeptIds.contains(childDeptId);
    }

    /**
     * 获取Aws消息详情
     * 
     * @param rowKey 数据行键
     * @return 详情数据
     */
    @ApiOperation("获取Aws消息详情")
    @PreAuthorize("@ss.hasPermi('devicemsg:aws:query')")
    @GetMapping("/detail/{rowKey}")
    public AjaxResult getDetail(@PathVariable("rowKey") String rowKey) {
        // 查询详情数据
        AwsHbaseVo detail = deviceDataService.getDeviceDetail(DEVICE_TYPE, rowKey, AwsHbaseVo.class);
        
        // 检查结果并返回
        if (detail == null) {
            return AjaxResult.error("未找到指定的数据记录");
        }
        return AjaxResult.success(detail);
    }
}
