package com.snct.web.controller.business;

import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.page.TableDataInfo;
import com.snct.common.enums.BusinessType;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.system.domain.DeviceAttribute;
import com.snct.system.service.IDeviceAttributeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备属性Controller
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
@RestController
@RequestMapping("/business/deviceattribute")
public class DeviceAttributeController extends BaseController
{
    @Autowired
    private IDeviceAttributeService deviceAttributeService;

    /**
     * 查询设备属性列表
     */
    @PreAuthorize("@ss.hasPermi('business:deviceattribute:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeviceAttribute deviceAttribute)
    {
        startPage();
        List<DeviceAttribute> list = deviceAttributeService.selectDeviceAttributeList(deviceAttribute);
        return getDataTable(list);
    }

    /**
     * 导出设备属性列表
     */
    @PreAuthorize("@ss.hasPermi('business:deviceattribute:export')")
    @Log(title = "设备属性", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DeviceAttribute deviceAttribute)
    {
        List<DeviceAttribute> list = deviceAttributeService.selectDeviceAttributeList(deviceAttribute);
        ExcelUtil<DeviceAttribute> util = new ExcelUtil<DeviceAttribute>(DeviceAttribute.class);
        util.exportExcel(response, list, "设备属性数据");
    }

    /**
     * 获取设备属性详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:deviceattribute:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(deviceAttributeService.selectDeviceAttributeById(id));
    }

    /**
     * 新增设备属性
     */
    @PreAuthorize("@ss.hasPermi('business:deviceattribute:add')")
    @Log(title = "设备属性", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DeviceAttribute deviceAttribute)
    {
        return toAjax(deviceAttributeService.insertDeviceAttribute(deviceAttribute));
    }

    /**
     * 修改设备属性
     */
    @PreAuthorize("@ss.hasPermi('business:deviceattribute:edit')")
    @Log(title = "设备属性", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DeviceAttribute deviceAttribute)
    {
        return toAjax(deviceAttributeService.updateDeviceAttribute(deviceAttribute));
    }

    /**
     * 删除设备属性
     */
    @PreAuthorize("@ss.hasPermi('business:deviceattribute:remove')")
    @Log(title = "设备属性", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(deviceAttributeService.deleteDeviceAttributeByIds(ids));
    }
}
