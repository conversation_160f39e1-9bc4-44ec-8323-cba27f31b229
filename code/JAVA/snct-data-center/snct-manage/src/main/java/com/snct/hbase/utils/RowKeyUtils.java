package com.snct.hbase.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * class
 *
 * <AUTHOR>
 */
public class RowKeyUtils {

    public static Long getTimeByRowKey(String rowKey) {
        if (StringUtils.isBlank(rowKey)) {
            return null;
        }
        String[] idArr = rowKey.split("\\|");

        return Long.valueOf(idArr[1]);
    }


    public static String getBjTimeByRowKey(String rowKey) {
        if (StringUtils.isBlank(rowKey)) {
            return null;
        }
        String[] idArr = rowKey.split("\\|");

        return DateUtils.getDateToString(Long.valueOf(idArr[1]));
    }
}
