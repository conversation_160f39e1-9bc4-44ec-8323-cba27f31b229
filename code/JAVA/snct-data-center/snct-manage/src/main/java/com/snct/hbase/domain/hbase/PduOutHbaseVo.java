package com.snct.hbase.domain.hbase;

import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;


/**
 * pdu-out消息对象 bu_msg_pdu_out
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
@HBaseTable(tableName = "snct:pduout")
public class PduOutHbaseVo
{

    /** ID */
    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;
    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

    /** 部门ID */
    @HBaseColumn(family = "i", qualifier = "d_p")
    private Long deptId;

    /** 设备id */
    @HBaseColumn(family = "i", qualifier = "d_i")
    private Long deviceId;

    /** 批次编号 */
    @HBaseColumn(family = "i", qualifier = "b_c")
    private String batchCode;

    /** 插座序号 */
    @Excel(name = "插座序号")
    @HBaseColumn(family = "i", qualifier = "o_i")
    private Long outIndex;

    /** 电流 */
    @Excel(name = "电流")
    @HBaseColumn(family = "i", qualifier = "e_t")
    private Double electric;

    /** 功率 */
    @Excel(name = "功率")
    @HBaseColumn(family = "i", qualifier = "p_w")
    private Double power;

    /** 插座状态 */
    @Excel(name = "插座状态")
    @HBaseColumn(family = "i", qualifier = "o_s")
    private Long outStatus;

    /** 状态 0默认 1发送云端成功 2发送云端失败 */
    @Excel(name = "状态 0默认 1发送云端成功 2发送云端失败")
    @HBaseColumn(family = "i", qualifier = "s_t")
    private Long status;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    public void setDeviceId(Long deviceId)
    {
        this.deviceId = deviceId;
    }

    public Long getDeviceId()
    {
        return deviceId;
    }

    public void setBatchCode(String batchCode) 
    {
        this.batchCode = batchCode;
    }

    public String getBatchCode() 
    {
        return batchCode;
    }

    public void setOutIndex(Long outIndex) 
    {
        this.outIndex = outIndex;
    }

    public Long getOutIndex() 
    {
        return outIndex;
    }

    public void setElectric(Double electric) 
    {
        this.electric = electric;
    }

    public Double getElectric() 
    {
        return electric;
    }

    public void setPower(Double power) 
    {
        this.power = power;
    }

    public Double getPower() 
    {
        return power;
    }

    public void setOutStatus(Long outStatus) 
    {
        this.outStatus = outStatus;
    }

    public Long getOutStatus() 
    {
        return outStatus;
    }

    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }
}
