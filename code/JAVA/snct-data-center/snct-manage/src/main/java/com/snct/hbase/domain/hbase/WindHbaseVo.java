package com.snct.hbase.domain.hbase;

import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;

/**
 * @description: 风速风向仪
 * @author: rr
 * @create: 2020-06-03 10:34
 **/
@HBaseTable
public class WindHbaseVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;
    /**
     * 部门id
     */
    @HBaseColumn(family = "i", qualifier = "d_p")
    private String deptId;

    /** sn */
    @HBaseColumn(family = "i", qualifier = "s_n")
    private String sn;
    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "i_t")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

    /**
     * 相对风向
     */
    @Excel(name="相对风向")
    @HBaseColumn(family = "i", qualifier = "r_w")
    private String relativeWind;

    /**
     * 相对风向标识
     */
    @Excel(name="相对风向标识")
    @HBaseColumn(family = "i", qualifier = "w_l_r")
    private String windLogoR;

    /**
     * 相对风速
     */
    @Excel(name="相对风速")
    @HBaseColumn(family = "i", qualifier = "r_w_s")
    private String relativeWindSpeed;

    /**
     * 真实风向
     */
    @Excel(name="真实风向")
    @HBaseColumn(family = "i", qualifier = "t_w")
    private String trueWind;

    /**
     * 真实风速
     */
    @Excel(name="真实风速")
    @HBaseColumn(family = "i", qualifier = "t_w_s")
    private String trueWindSpeed;

    /**
     * 真实风向标识
     */
    @Excel(name="真实风向标识")
    @HBaseColumn(family = "i", qualifier = "w_l_t")
    private String windLogoT;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setDeptId(String deptId)
    {
        this.deptId = deptId;
    }

    public String getDeptId()
    {
        return deptId;
    }

    public void setSn(String sn)
    {
        this.sn = sn;
    }

    public String getSn()
    {
        return sn;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getRelativeWind() {
        return relativeWind;
    }

    public void setRelativeWind(String relativeWind) {
        this.relativeWind = relativeWind;
    }

    public String getWindLogoR() {
        return windLogoR;
    }

    public void setWindLogoR(String windLogoR) {
        this.windLogoR = windLogoR;
    }

    public String getRelativeWindSpeed() {
        return relativeWindSpeed;
    }

    public void setRelativeWindSpeed(String relativeWindSpeed) {
        this.relativeWindSpeed = relativeWindSpeed;
    }

    public String getTrueWind() {
        return trueWind;
    }

    public void setTrueWind(String trueWind) {
        this.trueWind = trueWind;
    }

    public String getTrueWindSpeed() {
        return trueWindSpeed;
    }

    public void setTrueWindSpeed(String trueWindSpeed) {
        this.trueWindSpeed = trueWindSpeed;
    }

    public String getWindLogoT() {
        return windLogoT;
    }

    public void setWindLogoT(String windLogoT) {
        this.windLogoT = windLogoT;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

}
