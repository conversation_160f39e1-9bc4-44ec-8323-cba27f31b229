package com.snct.hbase.domain.engineroom;

import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * 压缩空气系统
 * <AUTHOR>
 */
public class CompressedAir {

    /**
     * 控制空气瓶压力  16015
     */
    private String caPress;

    /**
     * 1号主空气瓶压力  16012
     */
    private String maPress1;

    /**
     * 2号主空气瓶压力  16013
     */
    private String maPress2;

    /**
     * 辅空气瓶压力  16014
     */
    private String aaPress;

    /**
     * 1#发电机启动空气压力  01013
     */
    private String airPressure1;

    /**
     * 2#发电机启动空气压力  02013
     */
    private String airPressure2;

    /**
     * 3#发电机启动空气压力  03013
     */
    private String airPressure3;

    /**
     * 数据时间
     */
    private Long timeStamp;

    public String getCaPress() {
        return caPress;
    }

    public void setCaPress(String caPress) {
        this.caPress = caPress;
    }

    public String getMaPress1() {
        return maPress1;
    }

    public void setMaPress1(String maPress1) {
        this.maPress1 = maPress1;
    }

    public String getMaPress2() {
        return maPress2;
    }

    public void setMaPress2(String maPress2) {
        this.maPress2 = maPress2;
    }

    public String getAaPress() {
        return aaPress;
    }

    public void setAaPress(String aaPress) {
        this.aaPress = aaPress;
    }

    public String getAirPressure1() {
        return airPressure1;
    }

    public void setAirPressure1(String airPressure1) {
        this.airPressure1 = airPressure1;
    }

    public String getAirPressure2() {
        return airPressure2;
    }

    public void setAirPressure2(String airPressure2) {
        this.airPressure2 = airPressure2;
    }

    public String getAirPressure3() {
        return airPressure3;
    }

    public void setAirPressure3(String airPressure3) {
        this.airPressure3 = airPressure3;
    }

    public Long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public CompressedAir(){

    }

    public CompressedAir(Map<String,EngineroomData> map){

        this.caPress = AnalysisUtils.analysis(map.get("16015"));
        this.maPress1 = AnalysisUtils.analysis(map.get("16012"));
        this.maPress2 = AnalysisUtils.analysis(map.get("16013"));
        this.aaPress = AnalysisUtils.analysis(map.get("16014"));
    }

    public CompressedAir(String msg){

        String[] strbuff = msg.split(",",5);
        this.caPress = strbuff[0];
        this.maPress1 = strbuff[1];
        this.maPress2 = strbuff[2];
        this.aaPress = strbuff[3];
        this.airPressure1 = strbuff[4];
        this.airPressure2 = strbuff[5];
        this.airPressure3 = strbuff[6];
        this.timeStamp = Long.parseLong(strbuff[7]);
    }

    @Override
    public String toString() {
        return "CompressedAir{" +
                "caPress='" + caPress + '\'' +
                ", maPress1='" + maPress1 + '\'' +
                ", maPress2='" + maPress2 + '\'' +
                ", aaPress='" + aaPress + '\'' +
                ", airPressure1='" + airPressure1 + '\'' +
                ", airPressure2='" + airPressure2 + '\'' +
                ", airPressure3='" + airPressure3 + '\'' +
                ", timeStamp=" + timeStamp +
                '}';
    }

    public String mergeSendStr() {
        StringBuffer sb = new StringBuffer();
        sb.append(caPress == null ? "" : caPress).append(",");
        sb.append(maPress1 == null ? "" : maPress1).append(",");
        sb.append(maPress2 == null ? "" : maPress2).append(",");
        sb.append(aaPress == null ? "" : aaPress).append(",");
        sb.append(airPressure1 == null ? "" : airPressure1).append(",");
        sb.append(airPressure2 == null ? "" : airPressure2).append(",");
        sb.append(airPressure3 == null ? "" : airPressure3).append(",");
        sb.append(timeStamp == null ? "" : timeStamp);
        return sb.toString();
    }
}