package com.snct.hbase.domain.engineroom;

import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * 发电机
 * <AUTHOR>
 */
public class DieselGenerator {

    /**
     * 1#发电机转速  02002
     */
    private String speed;

    /**
     * 1#发电机负荷   02001
     */
    private String load;


    /**
     * 1#发电机透平增压器（左）排气温度  02046
     */
    private String megTempL;

    /**
     * 1#发电机透平增压器（右）排气温度  02047
     */
    private String megTempR;

    /**
     * 1#发电机冷却水出口温度  02042
     */
    private String cwoTemp;

    /**
     * 1#发电机空冷器出口温度   02039
     */
    private String actcs;

    /**
     * 1#发电机空冷器进口温度   02040
     */
    private String caths;

    /**
     * 1#发电机启动空气压力  02023
     */
    private String airPressure;

    /**
     * 1#发电机燃油进口压力  02024M
     */
    private String foiPress;

    /**
     * 1#发电机燃油消耗率  02003
     */
    private String fuelRate;

    /**
     * 1#发电机蓄电池电压  02004
     */
    private String bv;

    /**
     * 1#发电机滑油进口压力 02020M
     */
    private String inletPress;

    /**
     * 1#发电机滑油压差   02043
     */
    private String diffPressure;

    /**
     * 1#发电机海水进口压力  02025M
     */
    private String swiPress;

    /**
     * 1#发电机缸套冷却水进口压力 02007M
     */
    private String jcwip;

    /**
     * 1#发电机缸套水出口温度  02005M
     */
    private String jcwot;

    /**
     * 1#发电机冷却器后温度  02022
     */
    private String acTemp;

    /**
     * 1#发电机驱动端轴承温度  02035
     */
    private String btde;

    /**
     *1#发电机自由端轴承温度  02037
     */
    private String btnde;

    /**
     * 1#发电机定子绕组U 温度  02029
     */
    private String tempU;

    /**
     *  1#发电机定子绕组V 温度  02031
     */
    private String tempV;

    /**
     * 1#发电机定子绕组W 温度   02033
     */
    private String tempW;

    /**
     * 1#发电机1缸排气温度  01009.1
     */
    private String  egTemp1;

    /**
     * 1#发电机2缸排气温度  01009.2
     */
    private String  egTemp2;

    /**
     * 1#发电机3缸排气温度  01009.3
     */
    private String  egTemp3;

    /**
     * 1#发电机4缸排气温度   01009.4
     */
    private String  egTemp4;

    /**
     * 1#发电机5缸排气温度  01009.5
     */
    private String  egTemp5;

    /**
     * 1#发电机6缸排气温度   01009.6
     */
    private String  egTemp6;

    /**
     * 1#发电机7缸排气温度   01009.7
     */
    private String  egTemp7;

    /**
     * 1#发电机8缸排气温度   01009.8
     */
    private String  egTemp8;

    /**
     * 1#发电机9缸排气温度   01009.9
     */
    private String  egTemp9;

    /**
     * 1#发电机10缸排气温度   01009.10
     */
    private String  egTemp10;

    /**
     * 1#发电机11缸排气温度   01009.11
     */
    private String  egTemp11;

    /**
     * 1#发电机10缸排气温度   01009.12
     */
    private String  egTemp12;

    /**
     * 数据时间
     */
    private Long timeStamp;

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getLoad() {
        return load;
    }

    public void setLoad(String load) {
        this.load = load;
    }

    public String getMegTempL() {
        return megTempL;
    }

    public void setMegTempL(String megTempL) {
        this.megTempL = megTempL;
    }

    public String getMegTempR() {
        return megTempR;
    }

    public void setMegTempR(String megTempR) {
        this.megTempR = megTempR;
    }

    public String getCwoTemp() {
        return cwoTemp;
    }

    public void setCwoTemp(String cwoTemp) {
        this.cwoTemp = cwoTemp;
    }

    public String getActcs() {
        return actcs;
    }

    public void setActcs(String actcs) {
        this.actcs = actcs;
    }

    public String getCaths() {
        return caths;
    }

    public void setCaths(String caths) {
        this.caths = caths;
    }

    public String getAirPressure() {
        return airPressure;
    }

    public void setAirPressure(String airPressure) {
        this.airPressure = airPressure;
    }

    public String getFoiPress() {
        return foiPress;
    }

    public void setFoiPress(String foiPress) {
        this.foiPress = foiPress;
    }

    public String getFuelRate() {
        return fuelRate;
    }

    public void setFuelRate(String fuelRate) {
        this.fuelRate = fuelRate;
    }

    public String getBv() {
        return bv;
    }

    public void setBv(String bv) {
        this.bv = bv;
    }

    public String getInletPress() {
        return inletPress;
    }

    public void setInletPress(String inletPress) {
        this.inletPress = inletPress;
    }

    public String getDiffPressure() {
        return diffPressure;
    }

    public void setDiffPressure(String diffPressure) {
        this.diffPressure = diffPressure;
    }

    public String getSwiPress() {
        return swiPress;
    }

    public void setSwiPress(String swiPress) {
        this.swiPress = swiPress;
    }

    public String getJcwip() {
        return jcwip;
    }

    public void setJcwip(String jcwip) {
        this.jcwip = jcwip;
    }

    public String getJcwot() {
        return jcwot;
    }

    public void setJcwot(String jcwot) {
        this.jcwot = jcwot;
    }

    public String getAcTemp() {
        return acTemp;
    }

    public void setAcTemp(String acTemp) {
        this.acTemp = acTemp;
    }

    public String getBtde() {
        return btde;
    }

    public void setBtde(String btde) {
        this.btde = btde;
    }

    public String getBtnde() {
        return btnde;
    }

    public void setBtnde(String btnde) {
        this.btnde = btnde;
    }

    public String getTempU() {
        return tempU;
    }

    public void setTempU(String tempU) {
        this.tempU = tempU;
    }

    public String getTempV() {
        return tempV;
    }

    public void setTempV(String tempV) {
        this.tempV = tempV;
    }

    public String getTempW() {
        return tempW;
    }

    public void setTempW(String tempW) {
        this.tempW = tempW;
    }

    public String getEgTemp1() {
        return egTemp1;
    }

    public void setEgTemp1(String egTemp1) {
        this.egTemp1 = egTemp1;
    }

    public String getEgTemp2() {
        return egTemp2;
    }

    public void setEgTemp2(String egTemp2) {
        this.egTemp2 = egTemp2;
    }

    public String getEgTemp3() {
        return egTemp3;
    }

    public void setEgTemp3(String egTemp3) {
        this.egTemp3 = egTemp3;
    }

    public String getEgTemp4() {
        return egTemp4;
    }

    public void setEgTemp4(String egTemp4) {
        this.egTemp4 = egTemp4;
    }

    public String getEgTemp5() {
        return egTemp5;
    }

    public void setEgTemp5(String egTemp5) {
        this.egTemp5 = egTemp5;
    }

    public String getEgTemp6() {
        return egTemp6;
    }

    public void setEgTemp6(String egTemp6) {
        this.egTemp6 = egTemp6;
    }

    public String getEgTemp7() {
        return egTemp7;
    }

    public void setEgTemp7(String egTemp7) {
        this.egTemp7 = egTemp7;
    }

    public String getEgTemp8() {
        return egTemp8;
    }

    public void setEgTemp8(String egTemp8) {
        this.egTemp8 = egTemp8;
    }

    public String getEgTemp9() {
        return egTemp9;
    }

    public void setEgTemp9(String egTemp9) {
        this.egTemp9 = egTemp9;
    }

    public String getEgTemp10() {
        return egTemp10;
    }

    public void setEgTemp10(String egTemp10) {
        this.egTemp10 = egTemp10;
    }

    public String getEgTemp11() {
        return egTemp11;
    }

    public void setEgTemp11(String egTemp11) {
        this.egTemp11 = egTemp11;
    }

    public String getEgTemp12() {
        return egTemp12;
    }

    public void setEgTemp12(String egTemp12) {
        this.egTemp12 = egTemp12;
    }

    public Long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public DieselGenerator(){

    }

    /**
     *
     * @param sign 区分发动机 01：1号发动机   02：1号发动机
     * @param map
     */
    public DieselGenerator(String sign,Map<String,EngineroomData> map){

        this.speed = AnalysisUtils.analysis(map.get(sign+"002"));
        this.load = AnalysisUtils.analysis(map.get(sign+"001"));
        this.megTempL = AnalysisUtils.analysis(map.get(sign+"046"));
        this.megTempR = AnalysisUtils.analysis(map.get(sign+"047"));
        this.cwoTemp = AnalysisUtils.analysis(map.get(sign+"042"));
        this.actcs = AnalysisUtils.analysis(map.get(sign+"039"));
        this.caths = AnalysisUtils.analysis(map.get(sign+"040"));
        this.airPressure = AnalysisUtils.analysis(map.get(sign+"013"));
        this.foiPress = AnalysisUtils.analysis(map.get(sign+"014M"));
        this.fuelRate = AnalysisUtils.analysis(map.get(sign+"003"));
        this.bv = AnalysisUtils.analysis(map.get(sign+"004"));
        this.inletPress = AnalysisUtils.analysis(map.get(sign+"010M"));
        this.diffPressure = AnalysisUtils.analysis(map.get(sign+"043"));
        this.swiPress = AnalysisUtils.analysis(map.get(sign+"025M"));
        this.jcwip = AnalysisUtils.analysis(map.get(sign+"007M"));
        this.jcwot = AnalysisUtils.analysis(map.get(sign+"005M"));
        this.acTemp = AnalysisUtils.analysis(map.get(sign+"022"));
        this.btde = AnalysisUtils.analysis(map.get(sign+"035"));
        this.btnde = AnalysisUtils.analysis(map.get(sign+"037"));
        this.tempU = AnalysisUtils.analysis(map.get(sign+"029"));
        this.tempV = AnalysisUtils.analysis(map.get(sign+"031"));
        this.tempW = AnalysisUtils.analysis(map.get(sign+"033"));
        this.egTemp1 = AnalysisUtils.analysis(map.get(sign+"009.1"));
        this.egTemp2 = AnalysisUtils.analysis(map.get(sign+"009.2"));
        this.egTemp3 = AnalysisUtils.analysis(map.get(sign+"009.3"));
        this.egTemp4 = AnalysisUtils.analysis(map.get(sign+"009.4"));
        this.egTemp5 = AnalysisUtils.analysis(map.get(sign+"009.5"));
        this.egTemp6 = AnalysisUtils.analysis(map.get(sign+"009.6"));
        this.egTemp7 = AnalysisUtils.analysis(map.get(sign+"009.7"));
        this.egTemp8 = AnalysisUtils.analysis(map.get(sign+"009.8"));
        this.egTemp9 = AnalysisUtils.analysis(map.get(sign+"009.9"));
        this.egTemp10 = AnalysisUtils.analysis(map.get(sign+"009.10"));
        this.egTemp11 = AnalysisUtils.analysis(map.get(sign+"009.11"));
        this.egTemp12 = AnalysisUtils.analysis(map.get(sign+"009.12"));

    }


    public DieselGenerator(String msg){

        String[] strbuff = msg.split(",",35);
        this.speed = strbuff[0];
        this.load =strbuff[1];
        this.megTempL =strbuff[2];
        this.megTempR =strbuff[3];
        this.cwoTemp = strbuff[4];
        this.actcs =strbuff[5];
        this.caths =strbuff[6];
        this.airPressure =strbuff[7];
        this.foiPress = strbuff[8];
        this.fuelRate =strbuff[9];
        this.bv =strbuff[10];
        this.inletPress =strbuff[11];
        this.diffPressure = strbuff[12];
        this.swiPress =strbuff[13];
        this.jcwip =strbuff[14];
        this.jcwot =strbuff[15];
        this.acTemp =strbuff[16];
        this.btde =strbuff[17];
        this.btnde = strbuff[18];
        this.tempU =strbuff[19];
        this.tempV =strbuff[20];
        this.tempW =strbuff[21];
        this.egTemp1 = strbuff[22];
        this.egTemp2 = strbuff[23];
        this.egTemp3 = strbuff[24];
        this.egTemp4 = strbuff[25];
        this.egTemp5 = strbuff[26];
        this.egTemp6 = strbuff[27];
        this.egTemp7 = strbuff[28];
        this.egTemp8 = strbuff[29];
        this.egTemp9 = strbuff[30];
        this.egTemp10 = strbuff[31];
        this.egTemp11 = strbuff[32];
        this.egTemp12 = strbuff[33];
        this.timeStamp = Long.parseLong(strbuff[34]);
    }

    @Override
    public String toString() {
        return "DieselGenerator{" +
                "speed='" + speed + '\'' +
                ", load='" + load + '\'' +
                ", megTempL='" + megTempL + '\'' +
                ", megTempR='" + megTempR + '\'' +
                ", cwoTemp='" + cwoTemp + '\'' +
                ", actcs='" + actcs + '\'' +
                ", caths='" + caths + '\'' +
                ", airPressure='" + airPressure + '\'' +
                ", foiPress='" + foiPress + '\'' +
                ", fuelRate='" + fuelRate + '\'' +
                ", bv='" + bv + '\'' +
                ", inletPress='" + inletPress + '\'' +
                ", diffPressure='" + diffPressure + '\'' +
                ", swiPress='" + swiPress + '\'' +
                ", jcwip='" + jcwip + '\'' +
                ", jcwot='" + jcwot + '\'' +
                ", acTemp='" + acTemp + '\'' +
                ", btde='" + btde + '\'' +
                ", btnde='" + btnde + '\'' +
                ", tempU='" + tempU + '\'' +
                ", tempV='" + tempV + '\'' +
                ", tempW='" + tempW + '\'' +
                ", egTemp1='" + egTemp1 + '\'' +
                ", egTemp2='" + egTemp2 + '\'' +
                ", egTemp3='" + egTemp3 + '\'' +
                ", egTemp4='" + egTemp4 + '\'' +
                ", egTemp5='" + egTemp5 + '\'' +
                ", egTemp6='" + egTemp6 + '\'' +
                ", egTemp7='" + egTemp7 + '\'' +
                ", egTemp8='" + egTemp8 + '\'' +
                ", egTemp9='" + egTemp9 + '\'' +
                ", egTemp10='" + egTemp10 + '\'' +
                ", egTemp11='" + egTemp11 + '\'' +
                ", egTemp12='" + egTemp12 + '\'' +
                ", timeStamp=" + timeStamp +
                '}';
    }

    public String mergeSendStr() {
        StringBuffer sb = new StringBuffer();
        sb.append(speed == null ? "" : speed).append(",");
        sb.append(load == null ? "" : load).append(",");
        sb.append(megTempL == null ? "" : megTempL).append(",");
        sb.append(megTempR == null ? "" : megTempR).append(",");
        sb.append(cwoTemp == null ? "" : cwoTemp).append(",");
        sb.append(actcs == null ? "" : actcs).append(",");
        sb.append(caths == null ? "" : caths).append(",");
        sb.append(airPressure == null ? "" : airPressure).append(",");
        sb.append(foiPress == null ? "" : foiPress).append(",");
        sb.append(fuelRate == null ? "" : fuelRate).append(",");
        sb.append(bv == null ? "" : bv).append(",");
        sb.append(inletPress == null ? "" : inletPress).append(",");
        sb.append(diffPressure == null ? "" : diffPressure).append(",");
        sb.append(swiPress == null ? "" : swiPress).append(",");
        sb.append(jcwip == null ? "" : jcwip).append(",");
        sb.append(jcwot == null ? "" : jcwot).append(",");
        sb.append(acTemp == null ? "" : acTemp).append(",");
        sb.append(btde == null ? "" : btde).append(",");
        sb.append(btnde == null ? "" : btnde).append(",");
        sb.append(tempU == null ? "" : tempU).append(",");
        sb.append(tempV == null ? "" : tempV).append(",");
        sb.append(tempW == null ? "" : tempW).append(",");
        sb.append(egTemp1 == null ? "" : egTemp1).append(",");
        sb.append(egTemp2 == null ? "" : egTemp2).append(",");
        sb.append(egTemp3 == null ? "" : egTemp3).append(",");
        sb.append(egTemp4 == null ? "" : egTemp4).append(",");
        sb.append(egTemp5 == null ? "" : egTemp5).append(",");
        sb.append(egTemp6 == null ? "" : egTemp6).append(",");
        sb.append(egTemp7 == null ? "" : egTemp7).append(",");
        sb.append(egTemp8 == null ? "" : egTemp8).append(",");
        sb.append(egTemp9 == null ? "" : egTemp9).append(",");
        sb.append(egTemp10 == null ? "" : egTemp10).append(",");
        sb.append(egTemp11 == null ? "" : egTemp11).append(",");
        sb.append(egTemp12 == null ? "" : egTemp12).append(",");
        sb.append(timeStamp == null ? "" : timeStamp);

        return sb.toString();
    }

    public static void main(String[] args){
        String abc = "%|OK|27,RPM|OK|1802,°C|OK|367,°C|OK|353,°C|OK|32.4,°C|OK|34.2,°C|OK|43.5,kPa|OK|776,kPa|OK|496,l/h|OK|115,V|OK|26,kPa|OK|476,kPa|OK|64,kPa|OK|282,kPa|OK|209,°C|OK|80,°C|OK|29,°C|OK|45.8,°C|OK|36.7,°C|OK|44.1,°C|OK|43.5,°C|OK|42.9,°C|OK|306,°C|OK|323,°C|OK|306,°C|OK|307,°C|OK|335,°C|OK|306,°C|OK|330,°C|OK|335,°C|OK|293,°C|OK|307,°C|OK|270,°C|OK|270,1604629980000";
        DieselGenerator dieselGenerator = new DieselGenerator(abc);
        System.out.println(dieselGenerator);
    }
}
