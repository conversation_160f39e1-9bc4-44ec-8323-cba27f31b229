package com.snct.hbase.exception;

import com.snct.hbase.enums.IEnum;

/**
 * class
 *
 * <AUTHOR>
 * @date 2020/3/2 22:24
 */
public class SysException extends RuntimeException {

    protected Integer errorCode;

    public SysException(String message) {
        super(message);
    }

    public SysException(String message, Throwable cause) {
        super(message, cause);
    }

    public SysException(String message, IEnum iEnum) {
        super(message);
        this.errorCode = iEnum.getValue();
    }

    public SysException(IEnum iEnum) {
        super(iEnum.getAlias());
        this.errorCode = iEnum.getValue();
    }

    public SysException(IEnum iEnum, Throwable cause) {
        super(iEnum.getAlias(), cause);
    }
}
