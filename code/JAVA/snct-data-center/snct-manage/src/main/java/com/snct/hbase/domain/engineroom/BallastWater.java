package com.snct.hbase.domain.engineroom;

import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * 压载水系统
 * <AUTHOR>
 */
public class BallastWater {

    /**
     * 1号压载水舱水位  14026
     */
    private String level;

    /**
     * 6号压载水舱（左）水位  14035
     */
    private String levelP6;

    /**
     * 6号压载水舱（右）水位  14036
     */
    private String levelS6;

    /**
     * 4号压载水舱（左）水位  14031
     */
    private String levelP4;

    /**
     * 5号压载水舱（左）水位  14033
     */
    private String levelP5;

    /**
     * 5号压载水舱（右）水位  14034
     */
    private String levelS5;

    /**
     * 4号压载水舱（右）水位  14032
     */
    private String levelS4;

    /**
     * 吃水（后）  14040
     */
    private String draftAfter;

    /**
     * 吃水（左）  14038
     */
    private String draftP;

    /**
     * 吃水（右）  14039
     */
    private String draftS;

    /**
     * 吃水（前）  14037
     */
    private String draftFore;

    /**
     * 3号压载水舱（左）水位  14029
     */
    private String levelP3;

    /**
     * 2号压载水舱（左）水位  14027
     */
    private String levelP2;

    /**
     * 3号压载水舱（右）水位  14030
     */
    private String levelS3;

    /**
     * 2号压载水舱（右）水位  14028
     */
    private String levelS2;

    /**
     * 数据时间
     */
    private Long timeStamp;

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getLevelP6() {
        return levelP6;
    }

    public void setLevelP6(String levelP6) {
        this.levelP6 = levelP6;
    }

    public String getLevelS6() {
        return levelS6;
    }

    public void setLevelS6(String levelS6) {
        this.levelS6 = levelS6;
    }

    public String getLevelP4() {
        return levelP4;
    }

    public void setLevelP4(String levelP4) {
        this.levelP4 = levelP4;
    }

    public String getLevelP5() {
        return levelP5;
    }

    public void setLevelP5(String levelP5) {
        this.levelP5 = levelP5;
    }

    public String getLevelS5() {
        return levelS5;
    }

    public void setLevelS5(String levelS5) {
        this.levelS5 = levelS5;
    }

    public String getLevelS4() {
        return levelS4;
    }

    public void setLevelS4(String levelS4) {
        this.levelS4 = levelS4;
    }

    public String getDraftAfter() {
        return draftAfter;
    }

    public void setDraftAfter(String draftAfter) {
        this.draftAfter = draftAfter;
    }

    public String getDraftP() {
        return draftP;
    }

    public void setDraftP(String draftP) {
        this.draftP = draftP;
    }

    public String getDraftS() {
        return draftS;
    }

    public void setDraftS(String draftS) {
        this.draftS = draftS;
    }

    public String getDraftFore() {
        return draftFore;
    }

    public void setDraftFore(String draftFore) {
        this.draftFore = draftFore;
    }

    public String getLevelP3() {
        return levelP3;
    }

    public void setLevelP3(String levelP3) {
        this.levelP3 = levelP3;
    }

    public String getLevelP2() {
        return levelP2;
    }

    public void setLevelP2(String levelP2) {
        this.levelP2 = levelP2;
    }

    public String getLevelS3() {
        return levelS3;
    }

    public void setLevelS3(String levelS3) {
        this.levelS3 = levelS3;
    }

    public String getLevelS2() {
        return levelS2;
    }

    public void setLevelS2(String levelS2) {
        this.levelS2 = levelS2;
    }

    public Long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public BallastWater(Map<String,EngineroomData> map){
      
        this.level = AnalysisUtils.analysis(map.get("14026"));
        this.levelP6 = AnalysisUtils.analysis(map.get("14035"));
        this.levelS6 = AnalysisUtils.analysis(map.get("14036"));
        this.levelP4 = AnalysisUtils.analysis(map.get("14031"));
        this.levelP5 = AnalysisUtils.analysis(map.get("14033"));
        this.levelS5 = AnalysisUtils.analysis(map.get("14034"));
        this.levelS4 = AnalysisUtils.analysis(map.get("14032"));
        this.draftAfter = AnalysisUtils.analysis(map.get("14040"));
        this.draftP = AnalysisUtils.analysis(map.get("14038"));
        this.draftS = AnalysisUtils.analysis(map.get("14039"));
        this.draftFore = AnalysisUtils.analysis(map.get("14037"));
        this.levelP3 = AnalysisUtils.analysis(map.get("14029"));
        this.levelP2 = AnalysisUtils.analysis(map.get("14027"));
        this.levelS3 = AnalysisUtils.analysis(map.get("14030"));
        this.levelS2 = AnalysisUtils.analysis(map.get("14028"));

    }

    public BallastWater(String msg){

        String[] strbuff = msg.split(",",16);
        this.level = strbuff[0];
        this.levelP6 =strbuff[1];
        this.levelS6 =strbuff[2];
        this.levelP4 =strbuff[3];
        this.levelP5 =strbuff[4];
        this.levelS5 =strbuff[5];
        this.levelS4 =strbuff[6];
        this.draftAfter =strbuff[7];
        this.draftP =strbuff[8];
        this.draftS =strbuff[9];
        this.draftFore =strbuff[10];
        this.levelP3 =strbuff[11];
        this.levelP2 =strbuff[12];
        this.levelS3 =strbuff[13];
        this.levelS2 =strbuff[14];
        this.timeStamp = Long.parseLong(strbuff[15]);
    }

    @Override
    public String toString() {
        return "BallastWater{" +
                "level='" + level + '\'' +
                ", levelP6='" + levelP6 + '\'' +
                ", levelS6='" + levelS6 + '\'' +
                ", levelP4='" + levelP4 + '\'' +
                ", levelP5='" + levelP5 + '\'' +
                ", levelS5='" + levelS5 + '\'' +
                ", levelS4='" + levelS4 + '\'' +
                ", draftAfter='" + draftAfter + '\'' +
                ", draftP='" + draftP + '\'' +
                ", draftS='" + draftS + '\'' +
                ", draftFore='" + draftFore + '\'' +
                ", levelP3='" + levelP3 + '\'' +
                ", levelP2='" + levelP2 + '\'' +
                ", levelS3='" + levelS3 + '\'' +
                ", levelS2='" + levelS2 + '\'' +
                ", timeStamp=" + timeStamp +
                '}';
    }

    public String mergeSendStr() {
        StringBuffer sb = new StringBuffer();
        sb.append(level == null ? "" : level).append(",");
        sb.append(levelP6 == null ? "" : levelP6).append(",");
        sb.append(levelS6 == null ? "" : levelS6).append(",");
        sb.append(levelP4 == null ? "" : levelP4).append(",");
        sb.append(levelP5 == null ? "" : levelP5).append(",");
        sb.append(levelS5 == null ? "" : levelS5).append(",");
        sb.append(levelS4 == null ? "" : levelS4).append(",");

        sb.append(draftAfter == null ? "" : draftAfter).append(",");
        sb.append(draftP == null ? "" : draftP).append(",");
        sb.append(draftS == null ? "" : draftS).append(",");
        sb.append(draftFore == null ? "" : draftFore).append(",");
        sb.append(levelP3 == null ? "" : levelP3).append(",");
        sb.append(levelP2 == null ? "" : levelP2).append(",");
        sb.append(levelS3 == null ? "" : levelS3).append(",");
        sb.append(levelS2 == null ? "" : levelS2).append(",");
        sb.append(timeStamp == null ? "" : timeStamp);
        return sb.toString();
    }

}
