package com.snct.hbase.domain.engineroom.vo;

import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;
import com.snct.hbase.domain.engineroom.EngineroomData;
import com.snct.hbase.utils.AnalysisUtils;
import com.snct.hbase.utils.RowKeyUtils;

import java.util.Map;

/**
 * 主推侧推数据
 *
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:main_side_thrust")
public class MainSideThrustVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    private Long time;

    private String bjTime;

    /*----------------------------------主推4-----------------------------------------*/
    /**
     * 主推4马达转速  "05100"	"MT4 MTR SPEED"	"RPM"	"OK"	0
     */
    @Excel(name = "主推4马达转速")
    @HBaseColumn(family = "i", qualifier = "m4ms")
    private String mt4MtrSpeed;

    /**
     * 主推4 马达扭矩  "05101"	"MT4 MTR TORQUE"	"Nm"	"OK"	-1.8
     */
    @Excel(name = "主推4马达转速")
    @HBaseColumn(family = "i", qualifier = "m4mt")
    private String mt4MtrTorque;

    /**
     * 主推4 马达功率  "05102"	"MT4 MTR POWER"	"kW"	"OK"	-1.5
     */
    @Excel(name = "主推4马达转速")
    @HBaseColumn(family = "i", qualifier = "m4mp")
    private String mt4MtrPower;

    /**
     * 主推4 马达电压  "05103"	"MT4 MTR VOLTAGE"	"V"	"OK"	5
     */
    @Excel(name = "主推4马达转速")
    @HBaseColumn(family = "i", qualifier = "m4mv")
    private String mt4MtrVoltage;

    /**
     * 主推4 马达电流   "05104"	"MT4 MTR CURRENT"	"A"	"OK"	235.5
     */
    @Excel(name = "主推4马达转速")
    @HBaseColumn(family = "i", qualifier = "m4mc")
    private String mt4MtrCurrent;

    /**
     * ???   "05105"	"MT4 VFD IGBT TEMP."	"°C"	"OK"	27.4
     */
    @Excel(name = "主推4马达转速")
    @HBaseColumn(family = "i", qualifier = "m4vit")
    private String mt4VfdIgbtTemp;

    /**
     * 主推4 马达指令转速  "05106"	"MT4 MTR RPM COMMAND"	"RPM"	"OK"	0
     */
    @Excel(name = "主推4马达转速")
    @HBaseColumn(family = "i", qualifier = "m4mrc")
    private String mt4MtrRpmCommand;

    /**
     * 主推4 马达指令功率 "05107"	"MT4 MTR POWER COMMAND"	"kW"	"OK"	48.3
     */
    @Excel(name = "主推4马达转速")
    @HBaseColumn(family = "i", qualifier = "m4mpc")
    private String mt4MtrPowerCommand;

    /*----------------------------------主推4-----------------------------------------*/


    /*----------------------------------主推5-----------------------------------------*/
    /**
     * 主推5马达转速  "05237"	"MT5 MTR SPEED"	"RPM"	"OK"	0
     */
    @Excel(name = "主推5马达转速")
    @HBaseColumn(family = "i", qualifier = "m5ms")
    private String mt5MtrSpeed;

    /**
     * 主推5马达扭矩  "05238"	"MT5 MTR TORQUE"	"Nm"	"OK"	-0.6
     */
    @Excel(name = "主推5马达扭矩")
    @HBaseColumn(family = "i", qualifier = "m5mt")
    private String mt5MtrTorque;

    /**
     * 主推5马达功率  "05239"	"MT5 MTR POWER"	"kW"	"OK"	-1.5
     */
    @Excel(name = "主推5马达功率")
    @HBaseColumn(family = "i", qualifier = "m5mp")
    private String mt5MtrPower;

    /**
     * 主推5马达电压  "05240"	"MT5 MTR VOLTAGE"	"V"	"OK"	4
     */
    @Excel(name = "主推5马达电压")
    @HBaseColumn(family = "i", qualifier = "m5mv")
    private String mt5MtrVoltage;

    /**
     * 主推5马达电流   "05241"	"MT5 MTR CURRENT"	"A"	"OK"	222.3
     */
    @Excel(name = "主推5马达电流")
    @HBaseColumn(family = "i", qualifier = "m5mc")
    private String mt5MtrCurrent;

    /**
     * ???   "05242"	"MT5 VFD IGBT TEMP."	"°C"	"OK"	28.2
     */
    @Excel(name = "主推5??")
    @HBaseColumn(family = "i", qualifier = "m5vit")
    private String mt5VfdIgbtTemp;

    /**
     * 主推5马达指令转速  "05243"	"MT5 MTR RPM COMMAND"	"RPM"	"OK"	0
     */
    @Excel(name = "主推5马达指令转速")
    @HBaseColumn(family = "i", qualifier = "m5mrc")
    private String mt5MtrRpmCommand;
    /*----------------------------------主推5-----------------------------------------*/


    /*----------------------------------艏侧推 1-----------------------------------------*/
    /**
     * 艏侧推1马达转速  "07045"	"BT1 MTR SPEED"	"RPM"	"OK"	738.7
     */
    @Excel(name = "艏侧推1马达转速")
    @HBaseColumn(family = "i", qualifier = "b1ms")
    private String bt1MtrSpeed;

    /**
     * 艏侧推1马达扭矩  "07046"	"BT1 MTR TORQUE"	"Nm"	"OK"	23
     */
    @Excel(name = "艏侧推1马达扭矩")
    @HBaseColumn(family = "i", qualifier = "b1mt")
    private String bt1MtrTorque;

    /**
     * 艏侧推1马达功率  "07047"	"BT1 MTR POWER"	"kW"	"OK"	83.4
     */
    @Excel(name = "艏侧推1马达功率")
    @HBaseColumn(family = "i", qualifier = "b1mp")
    private String bt1MtrPower;

    /**
     * 艏侧推1马达电压  "07048"	"BT1 MTR VOLTAGE"	"V"	"OK"	426
     */
    @Excel(name = "艏侧推1马达电压")
    @HBaseColumn(family = "i", qualifier = "b1mv")
    private String bt1MtrVoltage;

    /**
     * 艏侧推1马达电流   "07049"	"BT1 MTR CURRENT"	"A"	"OK"	302.3
     */
    @Excel(name = "艏侧推1马达电流")
    @HBaseColumn(family = "i", qualifier = "b1mc")
    private String bt1MtrCurrent;

    /**
     * ???   "07050"	"BT1 VFD IGBT TEMP."	"°C"	"OK"	52
     */
    @Excel(name = "艏侧推1马达指令转速")
    @HBaseColumn(family = "i", qualifier = "b1vit")
    private String bt1VfdIgbtTemp;

    /**
     * 艏侧推1马达指令转速  "07051"	"BT1 MTR RPM COMMAND"	"RPM"	"OK"	738.6
     */
    @Excel(name = "艏侧推1马达指令转速")
    @HBaseColumn(family = "i", qualifier = "b1mrc")
    private String bt1MtrRpmCommand;
    /*----------------------------------艏侧推 1-----------------------------------------*/


    /*----------------------------------艉侧推2-----------------------------------------*/
    /**
     * 艉侧推2马达转速  "08041"	"ST2 MTR SPEED"	"RPM"	"OK"	112.8
     */
    @Excel(name = "艉侧推2马达转速")
    @HBaseColumn(family = "i", qualifier = "s2ms")
    private String st2MtrSpeed;

    /**
     * 艉侧推2马达扭矩  "08042"	"ST2 MTR TORQUE"	"Nm"	"OK"	-0.2
     */
    @Excel(name = "艉侧推2马达扭矩")
    @HBaseColumn(family = "i", qualifier = "s2mt")
    private String st2MtrTorque;

    /**
     * 艉侧推2马达功率  "08043"	"ST2 MTR POWER"	"kW"	"OK"	-0.4
     */
    @Excel(name = "艉侧推2马达功率")
    @HBaseColumn(family = "i", qualifier = "s2mp")
    private String st2MtrPower;

    /**
     * 艉侧推2马达电压  "08044"	"ST2 MTR VOLTAGE"	"V"	"OK"	22
     */
    @Excel(name = "艉侧推2马达电压")
    @HBaseColumn(family = "i", qualifier = "s2mv")
    private String st2MtrVoltage;

    /**
     * 艉侧推2马达电流   "08045"	"ST2 MTR CURRENT"	"A"	"OK"	44.2
     */
    @Excel(name = "艉侧推2马达电流")
    @HBaseColumn(family = "i", qualifier = "s2mc")
    private String st2MtrCurrent;

    /**
     * ???   "07050"	"08046"	"ST2 VFD IGBT TEMP."	"°C"	"OK"	38
     */
    @Excel(name = "艉侧推2??")
    @HBaseColumn(family = "i", qualifier = "s2vit")
    private String st2VfdIgbtTemp;

    /**
     * 艉侧推2马达指令转速  "08047"	"ST2 MTR RPM COMMAND"	"RPM"	"OK"	114.7
     */
    @Excel(name = "艉侧推2马达指令转速")
    @HBaseColumn(family = "i", qualifier = "s2mrc")
    private String st2MtrRpmCommand;
    /*----------------------------------艉侧推2-----------------------------------------*/


    /*----------------------------------艉侧推3-----------------------------------------*/
    /**
     * 艉侧推3马达转速  "09041"	"ST3 MTR SPEED"	"RPM"	"OK"	112.8
     */
    @Excel(name = "艉侧推3马达转速")
    @HBaseColumn(family = "i", qualifier = "s3ms")
    private String st3MtrSpeed;

    /**
     * 艉侧推3马达扭矩  "09042"	"ST3 MTR TORQUE"	"Nm"	"OK"	-0.2
     */
    @Excel(name = "艉侧推3马达扭矩")
    @HBaseColumn(family = "i", qualifier = "s3mt")
    private String st3MtrTorque;

    /**
     * 艉侧推3马达功率  "09043"	"ST3 MTR POWER"	"kW"	"OK"	-0.4
     */
    @Excel(name = "艉侧推3马达功率")
    @HBaseColumn(family = "i", qualifier = "s3mp")
    private String st3MtrPower;

    /**
     * 艉侧推3马达电压  "09044"	"ST3 MTR VOLTAGE"	"V"	"OK"	22
     */
    @Excel(name = "艉侧推3马达电压")
    @HBaseColumn(family = "i", qualifier = "s3mv")
    private String st3MtrVoltage;

    /**
     * 艉侧推3马达电流   "09045"	"ST3 MTR CURRENT"	"A"	"OK"	44.2
     */
    @Excel(name = "艉侧推3马达电流")
    @HBaseColumn(family = "i", qualifier = "s3mc")
    private String st3MtrCurrent;

    /**
     * ???   "07050"	"09046"	"ST3 VFD IGBT TEMP."	"°C"	"OK"	38
     */
    @Excel(name = "艉侧推3??")
    @HBaseColumn(family = "i", qualifier = "s3vit")
    private String st3VfdIgbtTemp;

    /**
     * 艉侧推3 马达指令转速  "09047"	"ST3 MTR RPM COMMAND"	"RPM"	"OK"	114.7
     */
    @Excel(name = "艉侧推3马达指令转速")
    @HBaseColumn(family = "i", qualifier = "s3mrc")
    private String st3MtrRpmCommand;
    /*----------------------------------艉侧推3-----------------------------------------*/


    public MainSideThrustVo() {
    }

    public MainSideThrustVo(Map<String, EngineroomData> map) {
        /*-----------------------主推4---------------------*/
        this.mt4MtrSpeed = AnalysisUtils.analysisValue(map.get("05100"));
        this.mt4MtrTorque = AnalysisUtils.analysisValue(map.get("05101"));
        this.mt4MtrPower = AnalysisUtils.analysisValue(map.get("05102"));
        this.mt4MtrVoltage = AnalysisUtils.analysisValue(map.get("05103"));
        this.mt4MtrCurrent = AnalysisUtils.analysisValue(map.get("05104"));
        this.mt4VfdIgbtTemp = AnalysisUtils.analysisValue(map.get("05105"));
        this.mt4MtrRpmCommand = AnalysisUtils.analysisValue(map.get("05106"));
        this.mt4MtrPowerCommand = AnalysisUtils.analysisValue(map.get("05107"));

        /*-----------------------主推5---------------------*/
        this.mt5MtrSpeed = AnalysisUtils.analysisValue(map.get("05237"));
        this.mt5MtrTorque = AnalysisUtils.analysisValue(map.get("05238"));
        this.mt5MtrPower = AnalysisUtils.analysisValue(map.get("05239"));
        this.mt5MtrVoltage = AnalysisUtils.analysisValue(map.get("05240"));
        this.mt5MtrCurrent = AnalysisUtils.analysisValue(map.get("05241"));
        this.mt5VfdIgbtTemp = AnalysisUtils.analysisValue(map.get("05242"));
        this.mt5MtrRpmCommand = AnalysisUtils.analysisValue(map.get("05243"));

        /*-----------------------艏侧推 1---------------------*/
        this.bt1MtrSpeed = AnalysisUtils.analysisValue(map.get("07045"));
        this.bt1MtrTorque = AnalysisUtils.analysisValue(map.get("07046"));
        this.bt1MtrPower = AnalysisUtils.analysisValue(map.get("07047"));
        this.bt1MtrVoltage = AnalysisUtils.analysisValue(map.get("07048"));
        this.bt1MtrCurrent = AnalysisUtils.analysisValue(map.get("07049"));
        this.bt1VfdIgbtTemp = AnalysisUtils.analysisValue(map.get("07050"));
        this.bt1MtrRpmCommand = AnalysisUtils.analysisValue(map.get("07051"));

        /*-----------------------艉侧推2---------------------*/
        this.st2MtrSpeed = AnalysisUtils.analysisValue(map.get("08041"));
        this.st2MtrTorque = AnalysisUtils.analysisValue(map.get("08042"));
        this.st2MtrPower = AnalysisUtils.analysisValue(map.get("08043"));
        this.st2MtrVoltage = AnalysisUtils.analysisValue(map.get("08044"));
        this.st2MtrCurrent = AnalysisUtils.analysisValue(map.get("08045"));
        this.st2VfdIgbtTemp = AnalysisUtils.analysisValue(map.get("08046"));
        this.st2MtrRpmCommand = AnalysisUtils.analysisValue(map.get("08047"));

        /*-----------------------艉侧推3---------------------*/
        this.st3MtrSpeed = AnalysisUtils.analysisValue(map.get("09041"));
        this.st3MtrTorque = AnalysisUtils.analysisValue(map.get("09042"));
        this.st3MtrPower = AnalysisUtils.analysisValue(map.get("09043"));
        this.st3MtrVoltage = AnalysisUtils.analysisValue(map.get("09044"));
        this.st3MtrCurrent = AnalysisUtils.analysisValue(map.get("09045"));
        this.st3VfdIgbtTemp = AnalysisUtils.analysisValue(map.get("09046"));
        this.st3MtrRpmCommand = AnalysisUtils.analysisValue(map.get("09047"));
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return RowKeyUtils.getTimeByRowKey(this.id);
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return RowKeyUtils.getBjTimeByRowKey(this.id);
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getMt4MtrSpeed() {
        return mt4MtrSpeed;
    }

    public void setMt4MtrSpeed(String mt4MtrSpeed) {
        this.mt4MtrSpeed = mt4MtrSpeed;
    }

    public String getMt4MtrTorque() {
        return mt4MtrTorque;
    }

    public void setMt4MtrTorque(String mt4MtrTorque) {
        this.mt4MtrTorque = mt4MtrTorque;
    }

    public String getMt4MtrPower() {
        return mt4MtrPower;
    }

    public void setMt4MtrPower(String mt4MtrPower) {
        this.mt4MtrPower = mt4MtrPower;
    }

    public String getMt4MtrVoltage() {
        return mt4MtrVoltage;
    }

    public void setMt4MtrVoltage(String mt4MtrVoltage) {
        this.mt4MtrVoltage = mt4MtrVoltage;
    }

    public String getMt4MtrCurrent() {
        return mt4MtrCurrent;
    }

    public void setMt4MtrCurrent(String mt4MtrCurrent) {
        this.mt4MtrCurrent = mt4MtrCurrent;
    }

    public String getMt4VfdIgbtTemp() {
        return mt4VfdIgbtTemp;
    }

    public void setMt4VfdIgbtTemp(String mt4VfdIgbtTemp) {
        this.mt4VfdIgbtTemp = mt4VfdIgbtTemp;
    }

    public String getMt4MtrRpmCommand() {
        return mt4MtrRpmCommand;
    }

    public void setMt4MtrRpmCommand(String mt4MtrRpmCommand) {
        this.mt4MtrRpmCommand = mt4MtrRpmCommand;
    }

    public String getMt4MtrPowerCommand() {
        return mt4MtrPowerCommand;
    }

    public void setMt4MtrPowerCommand(String mt4MtrPowerCommand) {
        this.mt4MtrPowerCommand = mt4MtrPowerCommand;
    }

    public String getMt5MtrSpeed() {
        return mt5MtrSpeed;
    }

    public void setMt5MtrSpeed(String mt5MtrSpeed) {
        this.mt5MtrSpeed = mt5MtrSpeed;
    }

    public String getMt5MtrTorque() {
        return mt5MtrTorque;
    }

    public void setMt5MtrTorque(String mt5MtrTorque) {
        this.mt5MtrTorque = mt5MtrTorque;
    }

    public String getMt5MtrPower() {
        return mt5MtrPower;
    }

    public void setMt5MtrPower(String mt5MtrPower) {
        this.mt5MtrPower = mt5MtrPower;
    }

    public String getMt5MtrVoltage() {
        return mt5MtrVoltage;
    }

    public void setMt5MtrVoltage(String mt5MtrVoltage) {
        this.mt5MtrVoltage = mt5MtrVoltage;
    }

    public String getMt5MtrCurrent() {
        return mt5MtrCurrent;
    }

    public void setMt5MtrCurrent(String mt5MtrCurrent) {
        this.mt5MtrCurrent = mt5MtrCurrent;
    }

    public String getMt5VfdIgbtTemp() {
        return mt5VfdIgbtTemp;
    }

    public void setMt5VfdIgbtTemp(String mt5VfdIgbtTemp) {
        this.mt5VfdIgbtTemp = mt5VfdIgbtTemp;
    }

    public String getMt5MtrRpmCommand() {
        return mt5MtrRpmCommand;
    }

    public void setMt5MtrRpmCommand(String mt5MtrRpmCommand) {
        this.mt5MtrRpmCommand = mt5MtrRpmCommand;
    }

    public String getBt1MtrSpeed() {
        return bt1MtrSpeed;
    }

    public void setBt1MtrSpeed(String bt1MtrSpeed) {
        this.bt1MtrSpeed = bt1MtrSpeed;
    }

    public String getBt1MtrTorque() {
        return bt1MtrTorque;
    }

    public void setBt1MtrTorque(String bt1MtrTorque) {
        this.bt1MtrTorque = bt1MtrTorque;
    }

    public String getBt1MtrPower() {
        return bt1MtrPower;
    }

    public void setBt1MtrPower(String bt1MtrPower) {
        this.bt1MtrPower = bt1MtrPower;
    }

    public String getBt1MtrVoltage() {
        return bt1MtrVoltage;
    }

    public void setBt1MtrVoltage(String bt1MtrVoltage) {
        this.bt1MtrVoltage = bt1MtrVoltage;
    }

    public String getBt1MtrCurrent() {
        return bt1MtrCurrent;
    }

    public void setBt1MtrCurrent(String bt1MtrCurrent) {
        this.bt1MtrCurrent = bt1MtrCurrent;
    }

    public String getBt1VfdIgbtTemp() {
        return bt1VfdIgbtTemp;
    }

    public void setBt1VfdIgbtTemp(String bt1VfdIgbtTemp) {
        this.bt1VfdIgbtTemp = bt1VfdIgbtTemp;
    }

    public String getBt1MtrRpmCommand() {
        return bt1MtrRpmCommand;
    }

    public void setBt1MtrRpmCommand(String bt1MtrRpmCommand) {
        this.bt1MtrRpmCommand = bt1MtrRpmCommand;
    }

    public String getSt2MtrSpeed() {
        return st2MtrSpeed;
    }

    public void setSt2MtrSpeed(String st2MtrSpeed) {
        this.st2MtrSpeed = st2MtrSpeed;
    }

    public String getSt2MtrTorque() {
        return st2MtrTorque;
    }

    public void setSt2MtrTorque(String st2MtrTorque) {
        this.st2MtrTorque = st2MtrTorque;
    }

    public String getSt2MtrPower() {
        return st2MtrPower;
    }

    public void setSt2MtrPower(String st2MtrPower) {
        this.st2MtrPower = st2MtrPower;
    }

    public String getSt2MtrVoltage() {
        return st2MtrVoltage;
    }

    public void setSt2MtrVoltage(String st2MtrVoltage) {
        this.st2MtrVoltage = st2MtrVoltage;
    }

    public String getSt2MtrCurrent() {
        return st2MtrCurrent;
    }

    public void setSt2MtrCurrent(String st2MtrCurrent) {
        this.st2MtrCurrent = st2MtrCurrent;
    }

    public String getSt2VfdIgbtTemp() {
        return st2VfdIgbtTemp;
    }

    public void setSt2VfdIgbtTemp(String st2VfdIgbtTemp) {
        this.st2VfdIgbtTemp = st2VfdIgbtTemp;
    }

    public String getSt2MtrRpmCommand() {
        return st2MtrRpmCommand;
    }

    public void setSt2MtrRpmCommand(String st2MtrRpmCommand) {
        this.st2MtrRpmCommand = st2MtrRpmCommand;
    }

    public String getSt3MtrSpeed() {
        return st3MtrSpeed;
    }

    public void setSt3MtrSpeed(String st3MtrSpeed) {
        this.st3MtrSpeed = st3MtrSpeed;
    }

    public String getSt3MtrTorque() {
        return st3MtrTorque;
    }

    public void setSt3MtrTorque(String st3MtrTorque) {
        this.st3MtrTorque = st3MtrTorque;
    }

    public String getSt3MtrPower() {
        return st3MtrPower;
    }

    public void setSt3MtrPower(String st3MtrPower) {
        this.st3MtrPower = st3MtrPower;
    }

    public String getSt3MtrVoltage() {
        return st3MtrVoltage;
    }

    public void setSt3MtrVoltage(String st3MtrVoltage) {
        this.st3MtrVoltage = st3MtrVoltage;
    }

    public String getSt3MtrCurrent() {
        return st3MtrCurrent;
    }

    public void setSt3MtrCurrent(String st3MtrCurrent) {
        this.st3MtrCurrent = st3MtrCurrent;
    }

    public String getSt3VfdIgbtTemp() {
        return st3VfdIgbtTemp;
    }

    public void setSt3VfdIgbtTemp(String st3VfdIgbtTemp) {
        this.st3VfdIgbtTemp = st3VfdIgbtTemp;
    }

    public String getSt3MtrRpmCommand() {
        return st3MtrRpmCommand;
    }

    public void setSt3MtrRpmCommand(String st3MtrRpmCommand) {
        this.st3MtrRpmCommand = st3MtrRpmCommand;
    }
}
