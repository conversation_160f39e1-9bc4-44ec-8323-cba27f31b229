<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuMsgPduMapper">
    
    <resultMap type="BuMsgPdu" id="BuMsgPduResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"  column="dept_name"  />
        <result property="shipId"    column="ship_id"    />
        <result property="shipName"  column="name"  />
        <result property="deviceId"    column="device_id"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="manage"    column="manage"    />
        <result property="electric"    column="electric"    />
        <result property="voltage"    column="voltage"    />
        <result property="yesPwoer"    column="yesPwoer"    />
        <result property="noPwoer"    column="noPwoer"    />
        <result property="seePwoer"    column="seePwoer"    />
        <result property="powerParam"    column="powerParam"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectBuMsgPduVo">
        select p.id, p.dept_id, d.dept_name, s.name, p.ship_id,p.device_id, p.batch_code, p.manage, p.electric, p.voltage, p.yesPwoer, p.noPwoer, p.seePwoer, p.powerParam, p.status, p.create_time
        from bu_msg_pdu p
        left join sys_dept d on p.dept_id = d.dept_id
        left join bu_ship s on p.ship_id = s.ship_id
    </sql>

    <select id="selectBuMsgPduList" parameterType="BuMsgPdu" resultMap="BuMsgPduResult">
        <include refid="selectBuMsgPduVo"/>
        <where>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="deptName != null and deptName != ''"> and d.dept_name like concat('%', #{deptName}, '%')</if>
            <if test="shipId != null "> and ship_id = #{shipId}</if>
            <if test="shipName != null and shipName != ''"> and s.name like concat('%', #{shipName}, '%')</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="batchCode != null and batchCode != ''"> and batch_code like concat('%', #{batchCode}, '%')</if>
            <if test="manage != null "> and manage = #{manage}</if>
            <if test="electric != null "> and electric = #{electric}</if>
            <if test="voltage != null "> and voltage = #{voltage}</if>
            <if test="yesPwoer != null "> and yesPwoer = #{yesPwoer}</if>
            <if test="noPwoer != null "> and noPwoer = #{noPwoer}</if>
            <if test="seePwoer != null "> and seePwoer = #{seePwoer}</if>
            <if test="powerParam != null "> and powerParam = #{powerParam}</if>
            <if test="status != null "> and status = #{status}</if>
            ${params.dataScope}
        </where>
    </select>

    <select id="selectBuMsgPduById" parameterType="Long" resultMap="BuMsgPduResult">
        <include refid="selectBuMsgPduVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuMsgPdu" parameterType="BuMsgPdu" useGeneratedKeys="true" keyProperty="id">
        insert into bu_msg_pdu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="shipId != null">ship_id,</if>
            <if test="deviceId != null ">device_id,</if>
            <if test="batchCode != null ">batch_code,</if>
            <if test="manage != null">manage,</if>
            <if test="electric != null">electric,</if>
            <if test="voltage != null">voltage,</if>
            <if test="yesPwoer != null">yesPwoer,</if>
            <if test="noPwoer != null">noPwoer,</if>
            <if test="seePwoer != null">seePwoer,</if>
            <if test="powerParam != null">powerParam,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="shipId != null">#{shipId},</if>
            <if test="deviceId != null ">#{deviceId},</if>
            <if test="batchCode != null ">#{batchCode},</if>
            <if test="manage != null">#{manage},</if>
            <if test="electric != null">#{electric},</if>
            <if test="voltage != null">#{voltage},</if>
            <if test="yesPwoer != null">#{yesPwoer},</if>
            <if test="noPwoer != null">#{noPwoer},</if>
            <if test="seePwoer != null">#{seePwoer},</if>
            <if test="powerParam != null">#{powerParam},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateBuMsgPdu" parameterType="BuMsgPdu">
        update bu_msg_pdu
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="shipId != null">ship_id = #{shipId},</if>
            <if test="deviceId != null ">device_id = #{deviceId},</if>
            <if test="batchCode != null ">batch_code = #{batchCode},</if>
            <if test="manage != null">manage = #{manage},</if>
            <if test="electric != null">electric = #{electric},</if>
            <if test="voltage != null">voltage = #{voltage},</if>
            <if test="yesPwoer != null">yesPwoer = #{yesPwoer},</if>
            <if test="noPwoer != null">noPwoer = #{noPwoer},</if>
            <if test="seePwoer != null">seePwoer = #{seePwoer},</if>
            <if test="powerParam != null">powerParam = #{powerParam},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuMsgPduById" parameterType="Long">
        delete from bu_msg_pdu where id = #{id}
    </delete>

    <delete id="deleteBuMsgPduByIds" parameterType="String">
        delete from bu_msg_pdu where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>