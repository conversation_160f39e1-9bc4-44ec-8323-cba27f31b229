package com.snct.system.service.impl;

import com.snct.common.utils.DateUtils;
import com.snct.system.domain.Ship;
import com.snct.system.domain.dto.ShipSimpleDto;
import com.snct.system.mapper.ShipMapper;
import com.snct.system.service.IShipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 船只Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
@Service
public class ShipServiceImpl implements IShipService 
{
    @Autowired
    private ShipMapper shipMapper;

    /**
     * 查询船只
     * 
     * @param shipId 船只主键
     * @return 船只
     */
    @Override
    public Ship selectShipByShipId(Long shipId)
    {
        return shipMapper.selectShipByShipId(shipId);
    }
    @Override
    public Ship selectShipByShipSn(String sn)
    {
        return shipMapper.selectShipByShipSn(sn);
    }

    /**
     * 查询船只列表
     * 
     * @param ship 船只
     * @return 船只
     */
    @Override
    public List<Ship> selectShipList(Ship ship)
    {
        return shipMapper.selectShipList(ship);
    }

    @Override
    public int selectShipListCount(Ship ship)
    {
        return shipMapper.selectShipListCount(ship);
    }

    /**
     * 根据部门ID查询船只列表
     *
     * @param deptId 部门ID
     * @return 船只集合
     */
    @Override
    public List<ShipSimpleDto> selectSimpleShipListByDeptId(Long deptId)
    {
        return shipMapper.selectSimpleShipListByDeptId(deptId);
    }

    /**
     * 新增船只
     * 
     * @param ship 船只
     * @return 结果
     */
    @Override
    public int insertShip(Ship ship)
    {
        ship.setCreateTime(DateUtils.getNowDate());
        return shipMapper.insertShip(ship);
    }

    /**
     * 修改船只
     * 
     * @param ship 船只
     * @return 结果
     */
    @Override
    public int updateShip(Ship ship)
    {
        ship.setUpdateTime(DateUtils.getNowDate());
        return shipMapper.updateShip(ship);
    }

    /**
     * 批量删除船只
     * 
     * @param shipIds 需要删除的船只主键
     * @return 结果
     */
    @Override
    public int deleteShipByShipIds(Long[] shipIds)
    {
        return shipMapper.deleteShipByShipIds(shipIds);
    }

    /**
     * 删除船只信息
     * 
     * @param shipId 船只主键
     * @return 结果
     */
    @Override
    public int deleteShipByShipId(Long shipId)
    {
        return shipMapper.deleteShipByShipId(shipId);
    }
}
