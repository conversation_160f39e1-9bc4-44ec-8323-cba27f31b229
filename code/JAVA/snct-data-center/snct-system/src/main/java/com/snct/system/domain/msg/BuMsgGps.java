package com.snct.system.domain.msg;

import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * GPS消息对象 bu_msg_gps
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public class BuMsgGps extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;
    
    /** 设备ID */
    private Long deviceId;
    
    /** 状态 0默认 1发送云端成功 2发送云端失败 */
    private Long status;
    
    /** 录入时间 */
    private String initialTime;

    /** 录入时间(北京时间yyyy-MM-dd HH:mm:ss) */
    @Excel(name = "录入时间")
    private String initialBjTime;

//  ------------------ GGA ------------------------
    /** UTC时间 */
    @Excel(name = "UTC时间")
    private String utcTime;

    /** 纬度半球 */
    @Excel(name = "纬度半球")
    private String latitudeHemisphere;
    
    /** 经度半球 */
    @Excel(name = "经度半球")
    private String longitudeHemisphere;
    
    /** 纬度 */
    @Excel(name = "纬度")
    private String latitude;
    
    /** 经度 */
    @Excel(name = "经度")
    private String longitude;

    /** 定位质量指示，0=定位无效 */
    @Excel(name = "定位质量指示")
    private String position;
    
    /** 使用卫星数量，从00到12(第一个零也将传送) */
    @Excel(name = "使用卫星数量")
    private String satellites;
    
    /** 水平精确度，0.5到99.9 */
    @Excel(name = "水平精确度")
    private String definition;
    
    /** 天线离海平面的高度，-9999.9到9999.9米 */
    @Excel(name = "天线离海平面的高度")
    private String antennaHeight;
    
    /** M指单位米 */
    private String antennaUnit;
    
    /** 大地水准面高度，-9999.9到9999.9米 */
    @Excel(name = "大地水准面高度")
    private String geoidHeight;
    
    /** M指单位米 */
    private String geoidUnit;
    
    /** 差分GPS数据期限(RTCMSC-104)，最后设立RTCM传送的秒数量 */
    @Excel(name = "差分GPS数据期限")
    private String timeLimit;

//  ------------------ GNS ------------------------
    /** (GNS)UTC时间 */
    @Excel(name = "GNS-UTC时间")
    private String gnsUtcTime;
    
    /** (GNS)纬度半球 */
    @Excel(name = "(GNS)纬度半球")
    private String gnsLatitudeHemisphere;
    
    /** (GNS)经度半球 */
    @Excel(name = "(GNS)经度半球")
    private String gnsLongitudeHemisphere;
    
    /** (GNS)纬度 */
    @Excel(name = "(GNS)纬度")
    private String gnsLatitude;
    
    /** (GNS)经度 */
    @Excel(name = "(GNS)经度")
    private String gnsLongitude;

    /** (GNS)水平精确度，0.5到99.9 */
    @Excel(name = "(GNS)水平精确度")
    private String gnsDefinition;
    
    /** (GNS)天线离海平面的高度，-9999.9到9999.9米 */
    @Excel(name = "(GNS)天线离海平面的高度")
    private String gnsAntennaHeight;
    
    /** (GNS)大地水准面高度，-9999.9到9999.9米 */
    @Excel(name = "(GNS)大地水准面高度")
    private String gnsGeoidHeight;

//  ------------------ VTG ------------------------
    /** 以正北为参考基准的地面航向(000~359度，前面的0也将被传输) */
    @Excel(name = "以正北为参考基准的地面航向")
    private String rightNorthCourse;

    private String rightUnit;
    
    /** 以磁北为参考基准的地面航向(000~359度，前面的0也将被传输) */
    @Excel(name = "以磁北为参考基准的地面航向")
    private String magneticNorthCourse;

    private String magneticUnit;
    
    /** 地面速率(000.0~999.9节，前面的0也将被传输) */
    @Excel(name = "地面速率")
    private String groundRateJ;

    private String rateJUnit;

    /** 地面速率(0000.0~1851.8公里/小时，前面的0也将被传输) */
    @Excel(name = "地面速率")
    private String groundRateKm;
    
    private String rateKmUnit;

//  ------------------ RMC ------------------------
    /** 地面速率(000.0~999.9节，前面的0也将被传输) */
    @Excel(name = "地面速率")
    private String groundRate;
    
    /** 地面航向(000.0~359.9度，以真北为参考基准，前面的0也将被传输) */
    @Excel(name = "地面航向")
    private String groundCourse;
    
    /** 磁偏角(000.0~180.0度，前面的0也将被传输)，没有数值 */
    @Excel(name = "磁偏角")
    private String magneticDeclination;
    
    /** 磁偏角方向，E(东)或W(西)，没有数值 */
    @Excel(name = "磁偏角方向")
    private String direction;
    
    /** 模式指示(仅NMEA01833.00版本输出，A=自主定位，D=差分，E=估算，N=数据无效) */
    @Excel(name = "模式指示")
    private String modeIndication;

//  ------------------ GBS ------------------------
    /** 预期中的纬度错误 */
    @Excel(name = "预期中的纬度错误")
    private String latError;

    /** 预期中的经度错误 */
    @Excel(name = "预期中的经度错误")
    private String longError;

    /** 预期中的海拔高度错误 */
    @Excel(name = "预期中的海拔高度错误")
    private String altError;

    /** 最可能失败卫星的ID号 */
    @Excel(name = "最可能失败卫星的ID号")
    private String satelliteId;

    /** 最有可能失效卫星的漏检概率 */
    @Excel(name = "最有可能失效卫星的漏检概率")
    private String probability;

    /** 偏差估计 */
    @Excel(name = "偏差估计")
    private String estimate;

    /** 偏差估计标准差 */
    @Excel(name = "偏差估计标准差")
    private String deviation;

//  ------------------ ZDA ------------------------
    /** 日 */
    @Excel(name = "日")
    private String day;

    /** 月 */
    @Excel(name = "月")
    private String month;

    /** 年 */
    @Excel(name = "年")
    private String year;

    /** 本地区域小时 */
    @Excel(name = "本地区域小时")
    private String localZoneHours;

    /** 本地区域分钟 */
    @Excel(name = "本地区域分钟")
    private String localZoneMinutes;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getUtcTime() {
        return utcTime;
    }

    public void setUtcTime(String utcTime) {
        this.utcTime = utcTime;
    }

    public String getLatitudeHemisphere() {
        return latitudeHemisphere;
    }

    public void setLatitudeHemisphere(String latitudeHemisphere) {
        this.latitudeHemisphere = latitudeHemisphere;
    }

    public String getLongitudeHemisphere() {
        return longitudeHemisphere;
    }

    public void setLongitudeHemisphere(String longitudeHemisphere) {
        this.longitudeHemisphere = longitudeHemisphere;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getSatellites() {
        return satellites;
    }

    public void setSatellites(String satellites) {
        this.satellites = satellites;
    }

    public String getDefinition() {
        return definition;
    }

    public void setDefinition(String definition) {
        this.definition = definition;
    }

    public String getAntennaHeight() {
        return antennaHeight;
    }

    public void setAntennaHeight(String antennaHeight) {
        this.antennaHeight = antennaHeight;
    }

    public String getAntennaUnit() {
        return antennaUnit;
    }

    public void setAntennaUnit(String antennaUnit) {
        this.antennaUnit = antennaUnit;
    }

    public String getGeoidHeight() {
        return geoidHeight;
    }

    public void setGeoidHeight(String geoidHeight) {
        this.geoidHeight = geoidHeight;
    }

    public String getGeoidUnit() {
        return geoidUnit;
    }

    public void setGeoidUnit(String geoidUnit) {
        this.geoidUnit = geoidUnit;
    }

    public String getTimeLimit() {
        return timeLimit;
    }

    public void setTimeLimit(String timeLimit) {
        this.timeLimit = timeLimit;
    }

    public String getRightNorthCourse() {
        return rightNorthCourse;
    }

    public void setRightNorthCourse(String rightNorthCourse) {
        this.rightNorthCourse = rightNorthCourse;
    }

    public String getRightUnit() {
        return rightUnit;
    }

    public void setRightUnit(String rightUnit) {
        this.rightUnit = rightUnit;
    }

    public String getMagneticNorthCourse() {
        return magneticNorthCourse;
    }

    public void setMagneticNorthCourse(String magneticNorthCourse) {
        this.magneticNorthCourse = magneticNorthCourse;
    }

    public String getMagneticUnit() {
        return magneticUnit;
    }

    public void setMagneticUnit(String magneticUnit) {
        this.magneticUnit = magneticUnit;
    }

    public String getGroundRateJ() {
        return groundRateJ;
    }

    public void setGroundRateJ(String groundRateJ) {
        this.groundRateJ = groundRateJ;
    }

    public String getRateJUnit() {
        return rateJUnit;
    }

    public void setRateJUnit(String rateJUnit) {
        this.rateJUnit = rateJUnit;
    }

    public String getGroundRateKm() {
        return groundRateKm;
    }

    public void setGroundRateKm(String groundRateKm) {
        this.groundRateKm = groundRateKm;
    }

    public String getRateKmUnit() {
        return rateKmUnit;
    }

    public void setRateKmUnit(String rateKmUnit) {
        this.rateKmUnit = rateKmUnit;
    }

    public String getGroundRate() {
        return groundRate;
    }

    public void setGroundRate(String groundRate) {
        this.groundRate = groundRate;
    }

    public String getGroundCourse() {
        return groundCourse;
    }

    public void setGroundCourse(String groundCourse) {
        this.groundCourse = groundCourse;
    }

    public String getMagneticDeclination() {
        return magneticDeclination;
    }

    public void setMagneticDeclination(String magneticDeclination) {
        this.magneticDeclination = magneticDeclination;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getModeIndication() {
        return modeIndication;
    }

    public void setModeIndication(String modeIndication) {
        this.modeIndication = modeIndication;
    }

    public String getGnsUtcTime() {
        return gnsUtcTime;
    }

    public void setGnsUtcTime(String gnsUtcTime) {
        this.gnsUtcTime = gnsUtcTime;
    }

    public String getGnsLatitudeHemisphere() {
        return gnsLatitudeHemisphere;
    }

    public void setGnsLatitudeHemisphere(String gnsLatitudeHemisphere) {
        this.gnsLatitudeHemisphere = gnsLatitudeHemisphere;
    }

    public String getGnsLongitudeHemisphere() {
        return gnsLongitudeHemisphere;
    }

    public void setGnsLongitudeHemisphere(String gnsLongitudeHemisphere) {
        this.gnsLongitudeHemisphere = gnsLongitudeHemisphere;
    }

    public String getGnsLatitude() {
        return gnsLatitude;
    }

    public void setGnsLatitude(String gnsLatitude) {
        this.gnsLatitude = gnsLatitude;
    }

    public String getGnsLongitude() {
        return gnsLongitude;
    }

    public void setGnsLongitude(String gnsLongitude) {
        this.gnsLongitude = gnsLongitude;
    }

    public String getGnsDefinition() {
        return gnsDefinition;
    }

    public void setGnsDefinition(String gnsDefinition) {
        this.gnsDefinition = gnsDefinition;
    }

    public String getGnsAntennaHeight() {
        return gnsAntennaHeight;
    }

    public void setGnsAntennaHeight(String gnsAntennaHeight) {
        this.gnsAntennaHeight = gnsAntennaHeight;
    }

    public String getGnsGeoidHeight() {
        return gnsGeoidHeight;
    }

    public void setGnsGeoidHeight(String gnsGeoidHeight) {
        this.gnsGeoidHeight = gnsGeoidHeight;
    }

    public String getLatError() {
        return latError;
    }

    public void setLatError(String latError) {
        this.latError = latError;
    }

    public String getLongError() {
        return longError;
    }

    public void setLongError(String longError) {
        this.longError = longError;
    }

    public String getAltError() {
        return altError;
    }

    public void setAltError(String altError) {
        this.altError = altError;
    }

    public String getSatelliteId() {
        return satelliteId;
    }

    public void setSatelliteId(String satelliteId) {
        this.satelliteId = satelliteId;
    }

    public String getProbability() {
        return probability;
    }

    public void setProbability(String probability) {
        this.probability = probability;
    }

    public String getEstimate() {
        return estimate;
    }

    public void setEstimate(String estimate) {
        this.estimate = estimate;
    }

    public String getDeviation() {
        return deviation;
    }

    public void setDeviation(String deviation) {
        this.deviation = deviation;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getLocalZoneHours() {
        return localZoneHours;
    }

    public void setLocalZoneHours(String localZoneHours) {
        this.localZoneHours = localZoneHours;
    }

    public String getLocalZoneMinutes() {
        return localZoneMinutes;
    }

    public void setLocalZoneMinutes(String localZoneMinutes) {
        this.localZoneMinutes = localZoneMinutes;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deviceId", getDeviceId())
            .append("status", getStatus())
            .append("initialTime", getInitialTime())
            .append("initialBjTime", getInitialBjTime())
            .append("utcTime", getUtcTime())
            .append("latitude", getLatitude())
            .append("longitude", getLongitude())
            .append("position", getPosition())
            .append("satellites", getSatellites())
            .append("createTime", getCreateTime())
            .toString();
    }
} 