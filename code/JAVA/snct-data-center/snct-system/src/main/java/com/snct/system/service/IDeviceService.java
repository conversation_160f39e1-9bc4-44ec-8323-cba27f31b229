package com.snct.system.service;

import com.snct.system.domain.Device;

import java.util.List;

/**
 * 设备Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface IDeviceService 
{
    /**
     * 查询设备
     * 
     * @param id 设备主键
     * @return 设备
     */
    public Device selectDeviceById(Long id);

    /**
     * 查询设备列表
     * 
     * @param device 设备
     * @return 设备集合
     */
    public List<Device> selectDeviceList(Device device);

    /**
     * 根据船舶SN和设备类型查询设备列表
     * 
     * @param sn 船舶SN
     * @param type 设备类型
     * @return 设备集合
     */
    public List<Device> selectSimpleDeviceListBySnAndType(String sn, Long type);

    /**
     * 新增设备
     * 
     * @param device 设备
     * @return 结果
     */
    public int insertDevice(Device device);

    /**
     * 修改设备
     * 
     * @param device 设备
     * @return 结果
     */
    public int updateDevice(Device device);

    /**
     * 批量删除设备
     * 
     * @param ids 需要删除的设备主键集合
     * @return 结果
     */
    public int deleteDeviceByIds(Long[] ids);

    /**
     * 删除设备信息
     * 
     * @param id 设备主键
     * @return 结果
     */
    public int deleteDeviceById(Long id);
}
