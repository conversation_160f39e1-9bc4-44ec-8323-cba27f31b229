package com.snct.system.domain.msg;

import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * pdu消息对象 bu_msg_pdu
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
public class BuMsgPdu extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 船只id */
    @Excel(name = "船只id")
    private Long shipId;

    /** 船舶名称 */
    @Excel(name = "船舶名称")
    private String shipName;

    /** 设备id */
    @Excel(name = "设备id")
    private Long deviceId;


    /** 批次编号 */
    @Excel(name = "批次编号")
    private String batchCode;

    /** 总电能 */
    @Excel(name = "总电能")
    private Double manage;

    /** 电流 */
    @Excel(name = "电流")
    private Double electric;

    /** 电压 */
    @Excel(name = "电压")
    private Double voltage;

    /** 有功功率 */
    @Excel(name = "有功功率")
    private Double yesPwoer;

    /** 无功功率 */
    @Excel(name = "无功功率")
    private Double noPwoer;

    /** 视在功率 */
    @Excel(name = "视在功率")
    private Double seePwoer;

    /** 功率因数 */
    @Excel(name = "功率因数")
    private Long powerParam;

    /** 状态 0默认 1发送云端成功 2发送云端失败 */
    @Excel(name = "状态 0默认 1发送云端成功 2发送云端失败")
    private Long status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    public String getDeptName()
    {
        return deptName;
    }

    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }

    public void setShipId(Long shipId) 
    {
        this.shipId = shipId;
    }

    public Long getShipId() 
    {
        return shipId;
    }

    public String getShipName()
    {
        return shipName;
    }

    public void setShipName(String shipName)
    {
        this.shipName = shipName;
    }

    public void setDeviceId(Long deviceId)
    {
        this.deviceId = deviceId;
    }

    public Long getDeviceId()
    {
        return deviceId;
    }

    public void setBatchCode(String batchCode)
    {
        this.batchCode = batchCode;
    }

    public String getBatchCode()
    {
        return batchCode;
    }

    public void setManage(Double manage) 
    {
        this.manage = manage;
    }

    public Double getManage() 
    {
        return manage;
    }

    public void setElectric(Double electric) 
    {
        this.electric = electric;
    }

    public Double getElectric() 
    {
        return electric;
    }

    public void setVoltage(Double voltage) 
    {
        this.voltage = voltage;
    }

    public Double getVoltage() 
    {
        return voltage;
    }

    public void setYesPwoer(Double yesPwoer) 
    {
        this.yesPwoer = yesPwoer;
    }

    public Double getYesPwoer() 
    {
        return yesPwoer;
    }

    public void setNoPwoer(Double noPwoer) 
    {
        this.noPwoer = noPwoer;
    }

    public Double getNoPwoer() 
    {
        return noPwoer;
    }

    public void setSeePwoer(Double seePwoer) 
    {
        this.seePwoer = seePwoer;
    }

    public Double getSeePwoer() 
    {
        return seePwoer;
    }

    public void setPowerParam(Long powerParam) 
    {
        this.powerParam = powerParam;
    }

    public Long getPowerParam() 
    {
        return powerParam;
    }

    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("deptName", getDeptName())
            .append("shipId", getShipId())
            .append("shipName", getShipName())
            .append("deviceId", getDeviceId())
            .append("manage", getManage())
            .append("electric", getElectric())
            .append("voltage", getVoltage())
            .append("yesPwoer", getYesPwoer())
            .append("noPwoer", getNoPwoer())
            .append("seePwoer", getSeePwoer())
            .append("powerParam", getPowerParam())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .toString();
    }
}
