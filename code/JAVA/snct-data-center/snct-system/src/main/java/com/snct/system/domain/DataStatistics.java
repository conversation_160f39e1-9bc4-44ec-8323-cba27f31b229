package com.snct.system.domain;

import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 采集-传输记录对象 bu_data_statistics
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public class DataStatistics extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 字符长度 */
    @Excel(name = "字符长度")
    private Integer characterLength;

    /** 设备类型（0-设备，1-快照） */
    @Excel(name = "设备类型", readConverterExp = "0=-设备，1-快照")
    private Integer deviceType;

    /** 数据类型（0-采集数据，1-传输数据） */
    @Excel(name = "数据类型", readConverterExp = "0=-采集数据，1-传输数据")
    private Integer dataType;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setCharacterLength(Integer characterLength)
    {
        this.characterLength = characterLength;
    }

    public Integer getCharacterLength()
    {
        return characterLength;
    }

    public void setDeviceType(Integer deviceType)
    {
        this.deviceType = deviceType;
    }

    public Integer getDeviceType()
    {
        return deviceType;
    }

    public void setDataType(Integer dataType)
    {
        this.dataType = dataType;
    }

    public Integer getDataType()
    {
        return dataType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("createTime", getCreateTime())
            .append("characterLength", getCharacterLength())
            .append("deviceType", getDeviceType())
            .append("dataType", getDataType())
            .toString();
    }
}
