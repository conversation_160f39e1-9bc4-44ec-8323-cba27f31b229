package com.snct.system.domain.msg;

import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * pdu-out消息对象 bu_msg_pdu_out
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
public class BuMsgPduOut extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 设备id */
    @Excel(name = "设备id")
    private Long deviceId;

    /** 批次编号 */
    @Excel(name = "批次编号")
    private String batchCode;

    /** 插座序号 */
    @Excel(name = "插座序号")
    private Long outIndex;

    /** 电流 */
    @Excel(name = "电流")
    private Double electric;

    /** 功率 */
    @Excel(name = "功率")
    private Double power;

    /** 插座状态 */
    @Excel(name = "插座状态")
    private Long outStatus;

    /** 状态 0默认 1发送云端成功 2发送云端失败 */
    @Excel(name = "状态 0默认 1发送云端成功 2发送云端失败")
    private Long status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    public String getDeptName()
    {
        return deptName;
    }

    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }

    public void setDeviceId(Long deviceId)
    {
        this.deviceId = deviceId;
    }

    public Long getDeviceId()
    {
        return deviceId;
    }

    public void setBatchCode(String batchCode) 
    {
        this.batchCode = batchCode;
    }

    public String getBatchCode() 
    {
        return batchCode;
    }

    public void setOutIndex(Long outIndex) 
    {
        this.outIndex = outIndex;
    }

    public Long getOutIndex() 
    {
        return outIndex;
    }

    public void setElectric(Double electric) 
    {
        this.electric = electric;
    }

    public Double getElectric() 
    {
        return electric;
    }

    public void setPower(Double power) 
    {
        this.power = power;
    }

    public Double getPower() 
    {
        return power;
    }

    public void setOutStatus(Long outStatus) 
    {
        this.outStatus = outStatus;
    }

    public Long getOutStatus() 
    {
        return outStatus;
    }

    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("deviceId", getDeviceId())
            .append("batchCode", getBatchCode())
            .append("outIndex", getOutIndex())
            .append("electric", getElectric())
            .append("power", getPower())
            .append("outStatus", getOutStatus())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .toString();
    }
}
