package com.snct.system.service.impl;

import com.snct.common.utils.DateUtils;
import com.snct.system.domain.DataStatistics;
import com.snct.system.mapper.DataStatisticsMapper;
import com.snct.system.service.IDataStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 采集-传输记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
@Service
public class DataStatisticsServiceImpl implements IDataStatisticsService 
{
    @Autowired
    private DataStatisticsMapper dataStatisticsMapper;

    /**
     * 查询采集-传输记录
     * 
     * @param id 采集-传输记录主键
     * @return 采集-传输记录
     */
    @Override
    public DataStatistics selectDataStatisticsById(Long id)
    {
        return dataStatisticsMapper.selectDataStatisticsById(id);
    }

    /**
     * 查询采集-传输记录列表
     * 
     * @param dataStatistics 采集-传输记录
     * @return 采集-传输记录
     */
    @Override
    public List<DataStatistics> selectDataStatisticsList(DataStatistics dataStatistics)
    {
        return dataStatisticsMapper.selectDataStatisticsList(dataStatistics);
    }

    /**
     * 新增采集-传输记录
     * 
     * @param dataStatistics 采集-传输记录
     * @return 结果
     */
    @Override
    public int insertDataStatistics(DataStatistics dataStatistics)
    {
        dataStatistics.setCreateTime(DateUtils.getNowDate());
        return dataStatisticsMapper.insertDataStatistics(dataStatistics);
    }

    /**
     * 修改采集-传输记录
     * 
     * @param dataStatistics 采集-传输记录
     * @return 结果
     */
    @Override
    public int updateDataStatistics(DataStatistics dataStatistics)
    {
        return dataStatisticsMapper.updateDataStatistics(dataStatistics);
    }

    /**
     * 批量删除采集-传输记录
     * 
     * @param ids 需要删除的采集-传输记录主键
     * @return 结果
     */
    @Override
    public int deleteDataStatisticsByIds(Long[] ids)
    {
        return dataStatisticsMapper.deleteDataStatisticsByIds(ids);
    }

    /**
     * 删除采集-传输记录信息
     * 
     * @param id 采集-传输记录主键
     * @return 结果
     */
    @Override
    public int deleteDataStatisticsById(Long id)
    {
        return dataStatisticsMapper.deleteDataStatisticsById(id);
    }
}
