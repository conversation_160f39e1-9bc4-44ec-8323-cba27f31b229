server:
  port: 8090
spring:
  application:
    name: snct-gateway
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      server-addr: localhost:8848    # nacos注册中心地址
    gateway:
      routes: # 网关路由配置
        - id: snct-manage      # 路由id，自定义，只要唯一即可
          # uri: http://127.0.0.1:8081   # 路由的目标地址 (直接写死地址的方式，不推荐)
          uri: lb://snct-manage    # 路由的目标地址 lb是负载均衡，后面跟服务名称(推荐)
          predicates: # 路由断言，判断请求是否符合路由规则的条件
            - Path=/snct-manage/**      # 按照路径匹配，以/user/开头的请求就符合要求
        - id: snct-visual      # 路由id，自定义，只要唯一即可
          # uri: http://127.0.0.1:8081   # 路由的目标地址 (直接写死地址的方式，不推荐)
          uri: lb://snct-visual    # 路由的目标地址 lb是负载均衡，后面跟服务名称(推荐)
          predicates: # 路由断言，判断请求是否符合路由规则的条件
            - Path=/snct-visual/**      # 按照路径匹配，以/user/开头的请求就符合要求
