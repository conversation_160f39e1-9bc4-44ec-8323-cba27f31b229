//package com.snct.hbase.utils;
//
//import org.apache.hadoop.conf.Configuration;
//import org.apache.hadoop.hbase.HBaseConfiguration;
//import org.apache.hadoop.hbase.client.ConnectionFactory;
//import org.junit.Before;
//import org.junit.Test;
//
//import java.io.IOException;
//import java.util.*;
//
//public class HBaseDaoUtilTest {
//
//    private HBaseDaoUtil hBaseDaoUtil;
//
//    @Before
//    public void setUp() throws IOException {
//        System.out.println("====== 测试初始化 ======");
//        Configuration conf = HBaseConfiguration.create();
//        conf.set("hbase.zookeeper.quorum", "192.168.100.179");
//        conf.set("hbase.zookeeper.property.clientPort", "2181");
//        conf.set("zookeeper.znode.parent", "/hbase");
//
//        try {
//            HConnectionFactory.connection = ConnectionFactory.createConnection(conf);
//            System.out.println("HBase连接初始化成功");
//        } catch (IOException e) {
//            System.err.println("HBase连接初始化失败: " + e.getMessage());
//        }
//
//        hBaseDaoUtil = new HBaseDaoUtil();
//    }
//
//    @Test
//    public void createNameSpace() {
//        System.out.println("\n====== 测试 createNameSpace ======");
//        try {
//            hBaseDaoUtil.createNameSpace("test_namespace");
//            System.out.println("创建命名空间成功");
//        } catch (Exception e) {
//            System.out.println("创建命名空间失败: " + e.getMessage());
//        }
//    }
//
//    @Test
//    public void dropNameSpace() {
//        System.out.println("\n====== 测试 dropNameSpace ======");
//        try {
//            hBaseDaoUtil.dropNameSpace("test_namespace");
//            System.out.println("删除命名空间成功");
//        } catch (Exception e) {
//            System.out.println("删除命名空间失败: " + e.getMessage());
//        }
//    }
//
//    @Test
//    public void tableExists() {
//        System.out.println("\n====== 测试 tableExists ======");
//        try {
//            boolean exists = hBaseDaoUtil.tableExists("test_table");
//            System.out.println("表是否存在: " + exists);
//        } catch (Exception e) {
//            System.out.println("检查表是否存在失败: " + e.getMessage());
//        }
//    }
//
//    @Test
//    public void createTable() {
//        System.out.println("\n====== 测试 createTable ======");
//        try {
//            Set<String> familyColumns = new HashSet<>();
//            familyColumns.add("family1");
//            hBaseDaoUtil.createTable("test_table", familyColumns);
//            System.out.println("创建表成功");
//        } catch (Exception e) {
//            System.out.println("创建表失败: " + e.getMessage());
//        }
//    }
//
//    @Test
//    public void testCreateTable() {
//        System.out.println("\n====== 测试批量创建表 ======");
//        try {
//            Set<String> tableNames = new HashSet<>();
//            tableNames.add("table1");
//            tableNames.add("table2");
//
//            Set<String> familyColumns = new HashSet<>();
//            familyColumns.add("family1");
//            familyColumns.add("family2");
//
//            hBaseDaoUtil.createTable(tableNames, familyColumns, null);
//            System.out.println("批量创建表成功");
//        } catch (Exception e) {
//            System.out.println("批量创建表失败: " + e.getMessage());
//        }
//    }
//
//    @Test
//    public void testCreateTable1() {
//        System.out.println("\n====== 测试创建预分区表 ======");
//        try {
//            Set<String> familyColumns = new HashSet<>();
//            familyColumns.add("family1");
//
//            hBaseDaoUtil.createTable("test_table_split", familyColumns, HBaseDaoUtil.DAY_SPLIT_KEYS);
//            System.out.println("创建预分区表成功");
//        } catch (Exception e) {
//            System.out.println("创建预分区表失败: " + e.getMessage());
//        }
//    }
//
//    @Test
//    public void tableRename() {
//        System.out.println("\n====== 测试表重命名 ======");
//        try {
//            // 创建源表
//            if (!hBaseDaoUtil.tableExists("old_table")) {
//                Set<String> familyColumns = new HashSet<>();
//                familyColumns.add("family1");
//                hBaseDaoUtil.createTable("old_table", familyColumns);
//                System.out.println("创建原表成功");
//            }
//
//            // 重命名表
//            hBaseDaoUtil.tableRename("old_table", "new_table");
//            System.out.println("重命名表成功");
//        } catch (Exception e) {
//            System.out.println("表重命名失败: " + e.getMessage());
//        }
//    }
//
//    @Test
//    public void dropTable() {
//        System.out.println("\n====== 测试删除表 ======");
//        try {
//            if (hBaseDaoUtil.tableExists("test_table")) {
//                hBaseDaoUtil.dropTable("test_table");
//                System.out.println("删除表成功");
//            } else {
//                System.out.println("表不存在，无需删除");
//            }
//        } catch (Exception e) {
//            System.out.println("删除表失败: " + e.getMessage());
//        }
//    }
//
//    @Test
//    public void testSave() {
//        System.out.println("\n====== 测试保存对象 ======");
//        try {
//            // 准备测试对象
//            TestBean bean = new TestBean("row001", "测试用户", "25");
//
//            // 创建表（如果不存在）
//            if (!hBaseDaoUtil.tableExists("test_bean")) {
//                Set<String> familyColumns = new HashSet<>();
//                familyColumns.add("info");
//                hBaseDaoUtil.createTable("test_bean", familyColumns);
//                System.out.println("创建测试表成功");
//            }
//
//            // 保存对象
//            boolean result = hBaseDaoUtil.save(bean);
//            System.out.println("保存对象结果: " + result);
//        } catch (Exception e) {
//            System.out.println("保存对象失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testSaveWithTableName() {
//        System.out.println("\n====== 测试指定表名保存对象 ======");
//        try {
//            // 准备测试对象
//            TestBean bean = new TestBean("row002", "测试用户2", "30");
//
//            // 创建表（如果不存在）
//            if (!hBaseDaoUtil.tableExists("test_table")) {
//                Set<String> familyColumns = new HashSet<>();
//                familyColumns.add("info");
//                hBaseDaoUtil.createTable("test_table", familyColumns);
//                System.out.println("创建测试表成功");
//            }
//
//            // 保存对象到指定表
//            hBaseDaoUtil.save("test_table", bean);
//            System.out.println("保存对象到指定表成功");
//        } catch (Exception e) {
//            System.out.println("保存对象到指定表失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testGet() {
//        System.out.println("\n====== 测试根据rowkey获取对象 ======");
//        try {
//            // 先保存测试数据
//            TestBean bean = new TestBean("row003", "测试获取", "35");
//
//            // 确保表存在
//            if (!hBaseDaoUtil.tableExists("test_bean")) {
//                Set<String> familyColumns = new HashSet<>();
//                familyColumns.add("info");
//                hBaseDaoUtil.createTable("test_bean", familyColumns);
//                System.out.println("创建测试表成功");
//            }
//
//            // 保存测试数据
//            hBaseDaoUtil.save(bean);
//            System.out.println("保存测试数据成功");
//
//            // 根据rowkey获取
//            List<TestBean> results = hBaseDaoUtil.get(new TestBean(), "row003");
//            System.out.println("获取结果数量: " + results.size());
//            for (TestBean result : results) {
//                System.out.println("获取对象: " + result);
//            }
//        } catch (Exception e) {
//            System.out.println("获取对象失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testDelete() {
//        System.out.println("\n====== 测试删除对象 ======");
//        try {
//            // 先保存测试数据
//            TestBean bean = new TestBean("row004", "测试删除", "40");
//
//            // 确保表存在
//            if (!hBaseDaoUtil.tableExists("test_bean")) {
//                Set<String> familyColumns = new HashSet<>();
//                familyColumns.add("info");
//                hBaseDaoUtil.createTable("test_bean", familyColumns);
//                System.out.println("创建测试表成功");
//            }
//
//            // 保存测试数据
//            hBaseDaoUtil.save(bean);
//            System.out.println("保存测试数据成功");
//
//            // 删除对象
//            hBaseDaoUtil.delete(bean, "row004");
//            System.out.println("删除对象成功");
//
//            // 验证删除结果
//            List<TestBean> results = hBaseDaoUtil.get(new TestBean(), "row004");
//            System.out.println("删除后查询结果数量: " + results.size());
//        } catch (Exception e) {
//            System.out.println("删除对象失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testDeleteFromTable() {
//        System.out.println("\n====== 测试从指定表删除 ======");
//        try {
//            // 创建表
//            if (!hBaseDaoUtil.tableExists("delete_test")) {
//                Set<String> familyColumns = new HashSet<>();
//                familyColumns.add("info");
//                hBaseDaoUtil.createTable("delete_test", familyColumns);
//                System.out.println("创建测试表成功");
//            }
//
//            // 删除指定表中的行
//            hBaseDaoUtil.delete("delete_test", "row001", "row002");
//            System.out.println("从指定表删除成功");
//        } catch (Exception e) {
//            System.out.println("从指定表删除失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testQueryScan() {
//        System.out.println("\n====== 测试条件查询 ======");
//        try {
//            // 先保存测试数据
//            TestBean bean1 = new TestBean("scan001", "扫描测试1", "20");
//            TestBean bean2 = new TestBean("scan002", "扫描测试2", "25");
//
//            // 确保表存在
//            if (!hBaseDaoUtil.tableExists("test_bean")) {
//                Set<String> familyColumns = new HashSet<>();
//                familyColumns.add("info");
//                hBaseDaoUtil.createTable("test_bean", familyColumns);
//                System.out.println("创建测试表成功");
//            }
//
//            // 保存测试数据
//            hBaseDaoUtil.save(bean1, bean2);
//            System.out.println("保存测试数据成功");
//
//            // 构建查询条件
//            Map<String, String> params = new HashMap<>();
//            params.put("name", "扫描测试1");
//
//            // 执行查询
//            List<TestBean> results = hBaseDaoUtil.queryScan(new TestBean(), params);
//            System.out.println("查询结果数量: " + (results != null ? results.size() : 0));
//            if (results != null) {
//                for (TestBean result : results) {
//                    System.out.println("查询结果: " + result);
//                }
//            }
//        } catch (Exception e) {
//            System.out.println("条件查询失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testFamilys() {
//        System.out.println("\n====== 测试获取表列簇 ======");
//        try {
//            // 创建测试表
//            if (!hBaseDaoUtil.tableExists("family_test")) {
//                Set<String> familyColumns = new HashSet<>();
//                familyColumns.add("f1");
//                familyColumns.add("f2");
//                familyColumns.add("f3");
//                hBaseDaoUtil.createTable("family_test", familyColumns);
//                System.out.println("创建测试表成功");
//            }
//
//            // 获取列簇
//            List<String> families = hBaseDaoUtil.familys("family_test");
//            System.out.println("列簇数量: " + families.size());
//            for (String family : families) {
//                System.out.println("列簇: " + family);
//            }
//        } catch (Exception e) {
//            System.out.println("获取表列簇失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testGetRowListByTime() {
//        System.out.println("\n====== 测试根据时间范围获取rowKey范围 ======");
//        try {
//            // 设置时间范围
//            long startTime = System.currentTimeMillis() - 24*60*60*1000; // 一天前
//            long endTime = System.currentTimeMillis();
//
//            // 获取rowKey范围
//            List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
//            System.out.println("rowKey范围数量: " + rowList.size());
//            for (int i = 0; i < Math.min(3, rowList.size()); i++) {
//                Map<String, String> row = rowList.get(i);
//                System.out.println("开始rowKey: " + row.get("sr") + ", 结束rowKey: " + row.get("er"));
//            }
//        } catch (Exception e) {
//            System.out.println("获取rowKey范围失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testGetRowKey() {
//        System.out.println("\n====== 测试获取rowKey ======");
//        try {
//            // 获取当前时间的rowKey
//            String rowKey = hBaseDaoUtil.getRowKey(System.currentTimeMillis());
//            System.out.println("生成的rowKey: " + rowKey);
//        } catch (Exception e) {
//            System.out.println("获取rowKey失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testGetTableName() {
//        System.out.println("\n====== 测试获取表名 ======");
//        try {
//            // 测试不同的获取表名方法
//            String tableName1 = hBaseDaoUtil.getTableName("device001", "type1", "code1", 0);
//            String tableName2 = hBaseDaoUtil.getTableName("device001", "type1");
//            String tableName3 = hBaseDaoUtil.getTableName("type1", "code1", 100);
//
//            System.out.println("表名1: " + tableName1);
//            System.out.println("表名2: " + tableName2);
//            System.out.println("表名3: " + tableName3);
//        } catch (Exception e) {
//            System.out.println("获取表名失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//}