# 数据源配置
spring:
    application:
        name: snct-visual  # 服务名称
    cloud:
        nacos:
            discovery:
                server-addr: localhost:8848  # Nacos 服务器地址
            config:
                server-addr: localhost:8848  # Nacos 服务器地址
                file-extension: yaml
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: ***************************************************************************************************************************************************
                username: root
                password: Snct##2025
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url: 
                username: 
                password: 
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter: 
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: snct
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
# hbase 配置
hbase:
    master: ***************
    zookeeper:
        quorum: ***************
        property:
            clientPort: 2181
zookeeper:
    znode:
        parent: /hbase

# 滑块验证码
aj:
    captcha:
        # 缓存类型
        cache-type: local
        # blockPuzzle 滑块 clickWord 文字点选  default默认两者都实例化
        type: blockPuzzle
        # 右下角显示字
        water-mark: szsnct
        # 校验滑动拼图允许误差偏移量(默认5像素)
        slip-offset: 5
        # aes加密坐标开启或者禁用(true|false)
        aes-status: true
        # 滑动干扰项(0/1/2)
        interference-options: 2
