package com.snct.hbase.enums;

/**
 * <AUTHOR>
 */
public enum EngineroomDataParam {
    /**
     *  数据位置参数
     */
    number(1,"编号或时间月的位置"),
    day(2,"日"),
    time(3,"时间"),
    year(4,"年"),
    symbol(5,"符号"),
    status(7,"状态"),
    data(8,"值"),
    length(9,"长度");

    private int  value;
    private String alias;

    EngineroomDataParam(int value, String alias) {
        this.value = value;
        this.alias = alias;
    }

    public int getValue() {
        return value;
    }

    public String getAlias() {
        return alias;
    }
}
