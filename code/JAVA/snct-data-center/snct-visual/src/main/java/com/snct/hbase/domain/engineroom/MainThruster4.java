package com.snct.hbase.domain.engineroom;

import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * 主推4
 * <AUTHOR>
 */
public class MainThruster4 {

    /**
     * 马达转速  05100
     */
    private String speed;

    /**
     * 马达扭矩  05101
     */
    private String torque;

    /**
     * 马达功率 05102
     */
    private String power;

    /**
     * 马达电压  05103
     */
    private String voltage;

    /**
     * 马达电流  05104
     */
    private String current;

    /**
     * VFD IGBT TEMP   05105
     */
    private String vfdIgbtTemp;

    /**
     * 马达绕组U温度   05091
     */
    private String wTempU;

    /**
     * 马达绕组U温度   05092
     */
    private String wTempV;

    /**
     * 马达绕组U温度   05093
     */
    private String wTempW;

    /**
     * VFD 水冷单元水压  05108
     */
    private String vfdwPress;

    /**
     * VFD 水冷单元水温  05109
     */
    private String vfdwTemp;

    /**
     * 数据时间
     */
    private Long timeStamp;

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getTorque() {
        return torque;
    }

    public void setTorque(String torque) {
        this.torque = torque;
    }

    public String getPower() {
        return power;
    }

    public void setPower(String power) {
        this.power = power;
    }

    public String getVoltage() {
        return voltage;
    }

    public void setVoltage(String voltage) {
        this.voltage = voltage;
    }

    public String getCurrent() {
        return current;
    }

    public void setCurrent(String current) {
        this.current = current;
    }

    public String getVfdIgbtTemp() {
        return vfdIgbtTemp;
    }

    public void setVfdIgbtTemp(String vfdIgbtTemp) {
        this.vfdIgbtTemp = vfdIgbtTemp;
    }

    public String getwTempU() {
        return wTempU;
    }

    public void setwTempU(String wTempU) {
        this.wTempU = wTempU;
    }

    public String getwTempV() {
        return wTempV;
    }

    public void setwTempV(String wTempV) {
        this.wTempV = wTempV;
    }

    public String getwTempW() {
        return wTempW;
    }

    public void setwTempW(String wTempW) {
        this.wTempW = wTempW;
    }

    public String getVfdwPress() {
        return vfdwPress;
    }

    public void setVfdwPress(String vfdwPress) {
        this.vfdwPress = vfdwPress;
    }

    public String getVfdwTemp() {
        return vfdwTemp;
    }

    public void setVfdwTemp(String vfdwTemp) {
        this.vfdwTemp = vfdwTemp;
    }

    public Long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public MainThruster4(){

    }

    public MainThruster4(Map<String,EngineroomData> map){

        this.speed = AnalysisUtils.analysis(map.get("05100"));
        this.torque = AnalysisUtils.analysis(map.get("05101"));
        this.power = AnalysisUtils.analysis(map.get("05102"));
        this.voltage = AnalysisUtils.analysis(map.get("05103"));
        this.current = AnalysisUtils.analysis(map.get("05104"));
        this.vfdIgbtTemp = AnalysisUtils.analysis(map.get("05105"));
        this.wTempU = AnalysisUtils.analysis(map.get("05091"));
        this.wTempV = AnalysisUtils.analysis(map.get("05092"));
        this.wTempW = AnalysisUtils.analysis(map.get("05093"));
        this.vfdwPress = AnalysisUtils.analysis(map.get("05108"));
        this.vfdIgbtTemp = AnalysisUtils.analysis(map.get("05109"));

    }

    public MainThruster4(String msg){

        String[] strbuff = msg.split(",",12);
        this.speed = strbuff[0];
        this.torque =strbuff[1];
        this.power =strbuff[2];
        this.voltage =strbuff[3];
        this.current = strbuff[4];
        this.vfdIgbtTemp =strbuff[5];
        this.wTempU =strbuff[6];
        this.wTempV =strbuff[7];
        this.wTempW = strbuff[8];
        this.vfdwPress =strbuff[9];
        this.vfdIgbtTemp =strbuff[10];
        this.timeStamp = Long.parseLong(strbuff[11]);
    }

    @Override
    public String toString() {
        return "MainThruster4{" +
                "speed='" + speed + '\'' +
                ", torque='" + torque + '\'' +
                ", power='" + power + '\'' +
                ", voltage='" + voltage + '\'' +
                ", current='" + current + '\'' +
                ", vfdIgbtTemp='" + vfdIgbtTemp + '\'' +
                ", wTempU='" + wTempU + '\'' +
                ", wTempV='" + wTempV + '\'' +
                ", wTempW='" + wTempW + '\'' +
                ", vfdwPress='" + vfdwPress + '\'' +
                ", vfdwTemp='" + vfdwTemp + '\'' +
                ", timeStamp=" + timeStamp +
                '}';
    }

    public String mergeSendStr() {
        StringBuffer sb = new StringBuffer();
        sb.append(speed == null ? "" : speed).append(",");
        sb.append(torque == null ? "" : torque).append(",");
        sb.append(power == null ? "" : power).append(",");
        sb.append(voltage == null ? "" : voltage).append(",");
        sb.append(current == null ? "" : current).append(",");
        sb.append(vfdIgbtTemp == null ? "" : vfdIgbtTemp).append(",");
        sb.append(wTempU == null ? "" : wTempU).append(",");
        sb.append(wTempV == null ? "" : wTempV).append(",");
        sb.append(wTempW == null ? "" : wTempW).append(",");
        sb.append(vfdwPress == null ? "" : vfdwPress).append(",");
        sb.append(vfdwTemp == null ? "" : vfdwTemp).append(",");
        sb.append(timeStamp == null ? "" : timeStamp);
        return sb.toString();
    }

    public static void main(String[] args){
        String a = "ExportData_20201019160900.txt";//29
        String b = "ExportData_";//11
        String c = "20201019160900";
        System.out.println(c.length());
    }
}

