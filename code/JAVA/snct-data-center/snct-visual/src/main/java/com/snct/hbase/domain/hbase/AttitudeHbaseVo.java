package com.snct.hbase.domain.hbase;


import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;

/**
 * @description: attitude数据 姿态仪  1秒多组
 *  Example:00000000h： 90 90 DB 00 02 00 F6 FF E8 29
 *  00000000h： 1 2 3 4 5 6 7 8 9 10
 *  Roll、Pitch、Heave、Heading分为低字节和高字节一次船东，二者组合成有符号的整型数据。
 *  数据二进制形式 空格隔开
 *  计算方法：
 * 横摇（左舷抬高为正）Roll=(Roll_H<<8|Roll_L)*0.01 		（单位：°）
 * 纵摇（船艏抬高为正）Pitch=(Pitch_H<<8|Pitch_L)*0.01 		（单位：°）
 * 升沉（向下为正）Heave=(Heave_H<<8|Heave_L)*0.01 		（单位：°）
 * 航向（正北0°）Heading=(Heading_H<<8|Heading_L)*0.01 （单位：m）
 * @author: rr
 * @create: 2020-06-15 14:47
 **/
@HBaseTable(tableName = "ns1:attitude-15")
public class AttitudeHbaseVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 部门id
     */
    @HBaseColumn(family = "i", qualifier = "d_p")
    private String deptId;

    /** sn */
    @HBaseColumn(family = "i", qualifier = "s_n")
    private String sn;

    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "i_t")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

    /**
     * 横摇
     */
    @Excel(name="横摇")
    @HBaseColumn(family = "i", qualifier = "roll")
    private String roll;

    /**
     * 纵摇
     */
    @Excel(name="纵摇")
    @HBaseColumn(family = "i", qualifier = "pitch")
    private String pitch;

    /**
     * 升沉
     */
    @Excel(name="升沉")
    @HBaseColumn(family = "i", qualifier = "heave")
    private String heave;

    /**
     * 航向
     */
    @Excel(name="航向")
    @HBaseColumn(family = "i", qualifier = "heading")
    private String heading;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRoll() {
        return roll;
    }

    public void setRoll(String roll) {
        this.roll = roll;
    }

    public String getPitch() {
        return pitch;
    }

    public void setPitch(String pitch) {
        this.pitch = pitch;
    }

    public String getHeave() {
        return heave;
    }

    public void setHeave(String heave) {
        this.heave = heave;
    }

    public String getHeading() {
        return heading;
    }

    public void setHeading(String heading) {
        this.heading = heading;
    }

    public void setDeptId(String deptId)
    {
        this.deptId = deptId;
    }

    public String getDeptId()
    {
        return deptId;
    }

    public void setSn(String sn)
    {
        this.sn = sn;
    }

    public String getSn()
    {
        return sn;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }
}
