package com.snct.common;

import com.alibaba.fastjson.JSONObject;
import com.snct.common.utils.spring.SpringUtils;
import com.snct.domain.AllDataVo;
import com.snct.service.GpsService;
import com.snct.service.RealtimeService;
import com.snct.service.WebSocketServlet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.websocket.Session;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * 定时任务
 */
@Component
public class CronSendData implements Runnable {
    private Logger logger = LoggerFactory.getLogger(CronSendData.class);
    private ConcurrentHashMap<Session, Map<String, String>> hashMap;

    public CronSendData() {
    }

    public CronSendData(ConcurrentHashMap<Session, Map<String, String>> hashMap) {
        this.hashMap = hashMap;
    }

    public void renewMap(ConcurrentHashMap<Session, Map<String, String>> hashMap){
        this.hashMap = hashMap;
    }

    static int count = 0; //计数器
    @Override
    public void run() {

        if (count%5==0) {
            //控制频率  第一次马上执行，后面每(count%x==0)x秒执行一次
            RealtimeService realtimeService = SpringUtils.getBean(RealtimeService.class);
            try {
                for (Map.Entry<Session, Map<String, String>> entry : WebSocketServlet.hashMap_Session.entrySet()) {
                    if(entry.getValue().get("codes")==null){
                        int now = (int)(System.currentTimeMillis()/1000);
                        if(now - Integer.parseInt(entry.getValue().get("time")) > 60 ){
                            //关闭无效连接
                            entry.getKey().close();
                            WebSocketServlet.hashMap_Session.remove(entry.getKey());
                        }
                        //过滤掉只建立了连接的Session
                        return;
                    }
                    String[] codeArr = entry.getValue().get("codes").split(",");
                    for (String code : codeArr) {
                        code = code.trim();
                        Object ob = realtimeService.getSrceenLatestData(entry.getValue().get("deptId"),entry.getValue().get("sn"), code);
                        if (ob != null) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("data", ob);
                            map.put("code", code);
                            map.put("deptId", entry.getValue().get("deptId"));
                            map.put("sn", entry.getValue().get("sn"));
                            sendData(entry.getKey(),map);
                        }
                    }
                    //sendData(entry.getKey(),map);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        count++;
    }

    private synchronized void sendData(Session session, Map<String, Object> allDataVo) {
        synchronized(session) {
            try {
                session.getBasicRemote().sendText(JSONObject.toJSONString(allDataVo));
            } catch (Exception e) {
                try {
                    //连接异常，关闭连接
                    session.close();
                    WebSocketServlet.hashMap_Session.remove(session);
                }catch (Exception e1) {}
                logger.error("websocket发送出错----{}", e);
            }
        }
    }

    /**
     * 查询共同数据
     */
    private void getCommonData(AllDataVo allDataVo, String sn) {
        GpsService gpsService = SpringUtils.getBean(GpsService.class);
//        gps数据
        allDataVo.setGpsData(JSONObject.toJSONString(gpsService.getLatestDataFromHbase(sn, "032A")));
    }

}