package com.snct.hbase.domain.hbase;

import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;

/**
 * @description: AWS数据   气象站  WIMWV  1秒一组（两条），WIXDR 10秒1组
 * @author: rr
 * @create: 2020-06-03 10:34
 **/
@HBaseTable
public class AwsHbaseVo {

    //---------------------- 基础信息字段 ----------------------
    /**
     * HBase行键
     */
    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 部门id
     */
    @HBaseColumn(family = "i", qualifier = "d_p")
    private String deptId;

    /** 部门名称 */
    @Excel(name="部门名称")
    @HBaseColumn(family = "i", qualifier = "d_p_n")
    private String deptName;

    /** 船只id */
    @HBaseColumn(family = "i", qualifier = "s_i")
    private Long shipId;

    /** 船只名称 */
    @Excel(name="船只名称")
    @HBaseColumn(family = "i", qualifier = "s_n")
    private String shipName;

    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "i_t")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

    //---------------------- 位置信息字段 ----------------------
    /**
     * 纬度
     */
    @Excel(name="纬度")
    @HBaseColumn(family = "i", qualifier = "i_l_a")
    private String latitude;

    /**
     * 经度
     */
    @Excel(name="经度")
    @HBaseColumn(family = "i", qualifier = "i_l_o")
    private String longitude;

    //---------------------- 风向风速数据 ($WIMWV) ----------------------
    /**
     * 相对风向
     */
    @Excel(name="相对风向")
    @HBaseColumn(family = "i", qualifier = "r_w")
    private String relativeWind;

    /**
     * 相对风向标识
     */
    @Excel(name="相对风向标识")
    @HBaseColumn(family = "i", qualifier = "w_l_r")
    private String windLogoR;

    /**
     * 相对风速
     */
    @Excel(name="相对风速")
    @HBaseColumn(family = "i", qualifier = "r_w_s")
    private String relativeWindSpeed;

    /**
     * 真实风向
     */
    @Excel(name="真实风向")
    @HBaseColumn(family = "i", qualifier = "t_w")
    private String trueWind;

    /**
     * 真实风速
     */
    @Excel(name="真实风速")
    @HBaseColumn(family = "i", qualifier = "t_w_s")
    private String trueWindSpeed;

    /**
     * 真实风向标识
     */
    @Excel(name="真实风向标识")
    @HBaseColumn(family = "i", qualifier = "w_l_t")
    private String windLogoT;

    /**
     * 风速单位 K=千米/小时  M=米/秒，N=海里/小时
     */
    @Excel(name="风速单位")
    @HBaseColumn(family = "i", qualifier = "wsu")
    private String windSpeedUnit;

    /**
     * 传感器状态
     */
    @Excel(name="传感器状态")
    @HBaseColumn(family = "i", qualifier = "s_s")
    private String sensorStatus;

    //---------------------- 气温数据 ($WIXDR) ----------------------
    /**
     * 传感器类型（气温）
     */
    @Excel(name="传感器类型（气温）")
    @HBaseColumn(family = "i", qualifier = "att")
    private String airTemType;

    /**
     * 气温值
     */
    @Excel(name="气温值")
    @HBaseColumn(family = "i", qualifier = "a_t")
    private String airTemperature;

    /**
     * 气温值单位（°C）
     */
    @Excel(name="气温值单位（°C）")
    @HBaseColumn(family = "i", qualifier = "au")
    private String airUnit;

    /**
     * 气温传感器ID
     */
    @Excel(name="气温传感器ID")
    @HBaseColumn(family = "i", qualifier = "as")
    private String airSensor;

    //---------------------- 湿度数据 ($WIXDR) ----------------------
    /**
     * 传感器类型（相对湿度）
     */
    @Excel(name="传感器类型（相对湿度）")
    @HBaseColumn(family = "i", qualifier = "ht")
    private String humidityType;

    /**
     * 相对湿度数值
     */
    @Excel(name="相对湿度数值")
    @HBaseColumn(family = "i", qualifier = "hum")
    private String humidity;

    /**
     * 相对湿度的单位
     */
    @Excel(name="相对湿度的单位")
    @HBaseColumn(family = "i", qualifier = "hu")
    private String humidityUnit;

    /**
     * 相对湿度传感器ID
     */
    @Excel(name="相对湿度传感器ID")
    @HBaseColumn(family = "i", qualifier = "hs")
    private String humiditySensor;

    //---------------------- 露点温度数据 ($WIXDR) ----------------------
    /**
     * 传感器类型（露点温度）
     */
    @Excel(name="传感器类型（露点温度）")
    @HBaseColumn(family = "i", qualifier = "ptt")
    private String pointTemType;

    /**
     * 露点温度数值
     */
    @Excel(name="露点温度数值")
    @HBaseColumn(family = "i", qualifier = "p_o")
    private String pointTem;

    /**
     * 露点温度传感器ID
     */
    @Excel(name="露点温度传感器ID")
    @HBaseColumn(family = "i", qualifier = "pts")
    private String pointTemSensor;

    //---------------------- 气压数据 ($WIXDR) ----------------------
    /**
     * 传感器类型（气压）
     */
    @Excel(name="传感器类型（气压）")
    @HBaseColumn(family = "i", qualifier = "pt")
    private String pressureType;

    /**
     * 气压数值
     */
    @Excel(name="气压数值")
    @HBaseColumn(family = "i", qualifier = "p_r")
    private String pressure;

    /**
     * 气压单位
     */
    @Excel(name="气压单位")
    @HBaseColumn(family = "i", qualifier = "p_u")
    private String pressureUnit;

    /**
     * 气压传感器ID
     */
    @Excel(name="气压传感器ID")
    @HBaseColumn(family = "i", qualifier = "ps")
    private String pressureSensor;

    //---------------------- 设备状态信息 ----------------------
    /**
     * 设备状态
     */
    @Excel(name="设备状态")
    @HBaseColumn(family = "i", qualifier = "status")
    private String status;

    /**
     * 设备ID
     */
    @Excel(name="设备ID")
    @HBaseColumn(family = "i", qualifier = "d_i")
    private String deviceId;

    // Getter and Setter methods
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setDeptId(String deptId)
    {
        this.deptId = deptId;
    }

    public String getDeptId()
    {
        return deptId;
    }

    public String getRelativeWind() {
        return relativeWind;
    }

    public void setRelativeWind(String relativeWind) {
        this.relativeWind = relativeWind;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Long getShipId() {
        return shipId;
    }

    public void setShipId(Long shipId) {
        this.shipId = shipId;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getWindLogoR() {
        return windLogoR;
    }

    public void setWindLogoR(String windLogoR) {
        this.windLogoR = windLogoR;
    }

    public String getRelativeWindSpeed() {
        return relativeWindSpeed;
    }

    public void setRelativeWindSpeed(String relativeWindSpeed) {
        this.relativeWindSpeed = relativeWindSpeed;
    }

    public String getTrueWind() {
        return trueWind;
    }

    public void setTrueWind(String trueWind) {
        this.trueWind = trueWind;
    }

    public String getTrueWindSpeed() {
        return trueWindSpeed;
    }

    public void setTrueWindSpeed(String trueWindSpeed) {
        this.trueWindSpeed = trueWindSpeed;
    }

    public String getWindLogoT() {
        return windLogoT;
    }

    public void setWindLogoT(String windLogoT) {
        this.windLogoT = windLogoT;
    }

    public String getAirTemperature() {
        return airTemperature;
    }

    public void setAirTemperature(String airTemperature) {
        this.airTemperature = airTemperature;
    }

    public String getHumidity() {
        return humidity;
    }

    public void setHumidity(String humidity) {
        this.humidity = humidity;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getPointTem() {
        return pointTem;
    }

    public void setPointTem(String pointTem) {
        this.pointTem = pointTem;
    }

    public String getPressure() {
        return pressure;
    }

    public void setPressure(String pressure) {
        this.pressure = pressure;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getWindSpeedUnit() {
        return windSpeedUnit;
    }

    public void setWindSpeedUnit(String windSpeedUnit) {
        this.windSpeedUnit = windSpeedUnit;
    }

    public String getAirTemType() {
        return airTemType;
    }

    public void setAirTemType(String airTemType) {
        this.airTemType = airTemType;
    }

    public String getAirUnit() {
        return airUnit;
    }

    public void setAirUnit(String airUnit) {
        this.airUnit = airUnit;
    }

    public String getAirSensor() {
        return airSensor;
    }

    public void setAirSensor(String airSensor) {
        this.airSensor = airSensor;
    }

    public String getHumidityType() {
        return humidityType;
    }

    public void setHumidityType(String humidityType) {
        this.humidityType = humidityType;
    }

    public String getHumidityUnit() {
        return humidityUnit;
    }

    public void setHumidityUnit(String humidityUnit) {
        this.humidityUnit = humidityUnit;
    }

    public String getHumiditySensor() {
        return humiditySensor;
    }

    public void setHumiditySensor(String humiditySensor) {
        this.humiditySensor = humiditySensor;
    }

    public String getPointTemType() {
        return pointTemType;
    }

    public void setPointTemType(String pointTemType) {
        this.pointTemType = pointTemType;
    }

    public String getPointTemSensor() {
        return pointTemSensor;
    }

    public void setPointTemSensor(String pointTemSensor) {
        this.pointTemSensor = pointTemSensor;
    }

    public String getPressureType() {
        return pressureType;
    }

    public void setPressureType(String pressureType) {
        this.pressureType = pressureType;
    }

    public String getPressureSensor() {
        return pressureSensor;
    }

    public void setPressureSensor(String pressureSensor) {
        this.pressureSensor = pressureSensor;
    }

    public String getPressureUnit() {
        return pressureUnit;
    }

    public void setPressureUnit(String pressureUnit) {
        this.pressureUnit = pressureUnit;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getSensorStatus() {
        return sensorStatus;
    }

    public void setSensorStatus(String sensorStatus) {
        this.sensorStatus = sensorStatus;
    }
}
