package com.snct.hbase.enums;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by Tsohan on 2020/2/27 0:51
 */
public class EnumTransUtils {

    private static Map<String, Map<String, String>> enumMap = Maps.newLinkedHashMap();


    /**
     * 转换枚举键值对为Map
     *
     * @param myEnum
     * @return
     */
    public static Map<String, String> transEnumMap(IEnum... myEnum) {
        Map<String, String> result = Maps.newLinkedHashMap();

        for (IEnum me : myEnum) {
            result.put(String.valueOf(me.getValue()), me.getAlias());
        }

        return result;
    }


    /**
     * 获取枚举的Map
     *
     * @param clazz
     * @return
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    public static Map<String, String> getEnumMap(Class clazz) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        if (null == clazz) {
            return null;
        }

        if (MapUtils.isEmpty(enumMap.get(clazz.getName()))) {
            Method method = clazz.getMethod("values");
            enumMap.put(clazz.getName(), transEnumMap((IEnum[]) method.invoke(null, null)));
        }

        return enumMap.get(clazz.getName());
    }


    /**
     * 把IEnum枚举数组转换成value的set
     *
     * @param enums
     * @return
     */
    public static Set<Integer> transformValues(IEnum... enums) {
        Set<Integer> values = new HashSet<>();
        for (IEnum e : enums) {
            values.add(e.getValue());
        }
        return values;
    }


    /**
     * 把enums数组转换成select所需的list
     *
     * @param enums
     * @return
     */
    public static List<ValueAlias> transformToSelectDatas(IEnum... enums) {
        List<ValueAlias> list = Lists.newArrayList();
        for (IEnum e : enums) {
            boolean hasSame = false;
            for (ValueAlias va : list) {
                if (va.getAlias().equals(e.getAlias())) {
                    va.setValue(e.getValue());
                    hasSame = true;
                    break;
                }
            }

            if (!hasSame) {
                list.add(new ValueAlias(e.getValue(), e.getAlias()));
            }
        }

        return list;
    }


    /**
     * 判断枚举值中是否存在value值
     *
     * @param clazz
     * @param value
     * @return
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    public static boolean containsValue(Class clazz, String value) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        if (clazz == null) {
            return false;
        }

        Map<String, String> enumMap = EnumTransUtils.getEnumMap(clazz);
        if (MapUtils.isEmpty(enumMap)) {
            return false;
        }

        return enumMap.keySet().contains(value);
    }


    /**
     * 判断枚举中是否存在alias值
     *
     * @param clazz
     * @param alias
     * @return
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    public static boolean containsAlias(Class clazz, String alias) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        if (clazz == null) {
            return false;
        }

        Map<String, String> enumMap = EnumTransUtils.getEnumMap(clazz);
        if (MapUtils.isEmpty(enumMap)) {
            return false;
        }

        return enumMap.values().contains(alias);
    }


    /**
     * 通过枚举名获取枚举实例
     *
     * @param clazz
     * @param name
     * @param <T_Method>
     * @return
     */
    public static <T_Method> T_Method getEnumByName(Class<T_Method> clazz, String name) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        if (null == clazz) {
            return null;
        }

        Method method = clazz.getDeclaredMethod("values");
        IEnum[] enums = (IEnum[]) method.invoke(clazz);
        for (IEnum e : enums) {
            if (StringUtils.equals(e.toString(), name)) {
                return (T_Method) e;
            }
        }
        return null;
    }


    /**
     * 通过value获取枚举实例
     *
     * @param clazz
     * @param value
     * @param <T_Method>
     * @return
     */
    public static <T_Method> T_Method getEnumByValue(Class<T_Method> clazz, Integer value) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        if (null == clazz) {
            return null;
        }

        Method method = clazz.getDeclaredMethod("values");
        IEnum[] enums = (IEnum[]) method.invoke(clazz);
        for (IEnum e : enums) {
            if (e.getValue().equals(value)) {
                return (T_Method) e;
            }
        }
        return null;
    }


    /**
     * 通过value获取枚举Alias
     *
     * @param clazz
     * @param value
     * @param <T_Method>
     * @return
     */
    public static <T_Method> String getAliasByValue(Class<T_Method> clazz, Integer value) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        IEnum enumObj = (IEnum) getEnumByValue(clazz, value);

        if (null == enumObj){
            throw new RuntimeException("无效的枚举类型或参数");
        }

        return enumObj.getAlias();
    }


    public static class ValueAlias {
        private String value;
        private String alias;
        private Set<Integer> valueSet;

        public ValueAlias() {
            valueSet = new HashSet<Integer>();
        }

        public ValueAlias(Integer value, String alias) {
            this.alias = alias;
            valueSet = new HashSet<Integer>();
            this.setValue(value);
        }

        public String getValue() {
            return value;
        }

        public void setValue(Integer value) {
            valueSet.add(value);
            this.value = JSON.toJSONString(valueSet);
        }

        public String getAlias() {
            return alias;
        }

        public void setAlias(String alias) {
            this.alias = alias;
        }
    }


}
