package com.snct.hbase.domain.engineroom.vo;

import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;
import com.snct.hbase.domain.engineroom.EngineroomData;
import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * 电力管理系统数据
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:power_manage")
public class PowerManageVo {

    @HBaseColumn(family = "rowkey",qualifier = "rowkey")
    String id;

    private Long time;

    private String bjTime;

    /**
     *主配电板频率  DG1-BUS_FREQ
     */
    @Excel(name="主配电板频率")
    @HBaseColumn(family = "i",qualifier = "msbf")
    private String msbFrequency;

    /**
     *主配电板1号发电机电压  DG1-BUS-VOLT
     */
    @Excel(name="主配电板1号发电机电压")
    @HBaseColumn(family = "i",qualifier = "msbvd1")
    private String msbVoltageDg1;

    /**
     *1号发电机负荷  G1-LOAD
     */
    @Excel(name="1号发电机负荷")
    @HBaseColumn(family = "i",qualifier = "loaddg1")
    private String loadDg1;

    /**
     *1号发电机有功功率  G1-AVA-POW
     */
    @Excel(name="1号发电机有功功率")
    @HBaseColumn(family = "i",qualifier = "dg1ap")
    private String dg1AvailablePower;

    /**
     *1号发电机电流L1  G1-I-L1
     */
    @Excel(name="1号发电机电流L1")
    @HBaseColumn(family = "i",qualifier = "dg1cl1")
    private String dg1CurrentL1;

    /**
     *2号发电机电流L1  G2-I-L1
     */
    @Excel(name="2号发电机电流L1")
    @HBaseColumn(family = "i",qualifier = "dg2cl1")
    private String dg2CurrentL1;

    /**
     *3号发电机电流L1  G3-I-L1
     */
    @Excel(name="3号发电机电流L1")
    @HBaseColumn(family = "i",qualifier = "dg3cl1")
    private String dg3CurrentL1;

    /**
     * 1号发电机电压 DG1-VOLTAGE
     */
    @Excel(name="1号发电机电压")
    @HBaseColumn(family = "i",qualifier = "dg1v")
    private String dg1Voltage;

    /**
     * 2号发电机电压 DG2-VOLTAGE
     */
    @Excel(name="2号发电机电压")
    @HBaseColumn(family = "i",qualifier = "dg2v")
    private String dg2Voltage;

    /**
     * 3号发电机电压 DG3-VOLTAGE
     */
    @Excel(name="3号发电机电压")
    @HBaseColumn(family = "i",qualifier = "dg3v")
    private String dg3Voltage;

    /**
     * 1号发电机频率 DG1-FREQ
     */
    @Excel(name="1号发电机频率")
    @HBaseColumn(family = "i",qualifier = "dg1f")
    private String dg1Frequency;

    /**
     * 2号发电机频率 DG2-FREQ
     */
    @Excel(name="2号发电机频率")
    @HBaseColumn(family = "i",qualifier = "dg2f")
    private String dg2Frequency;

    /**
     * 3号发电机频率 DG3-FREQ
     */
    @Excel(name="3号发电机频率")
    @HBaseColumn(family = "i",qualifier = "dg3f")
    private String dg3Frequency;

    /**
     * 1号发电机功率因数 DG1-COS_PHI
     */
    @Excel(name="1号发电机功率因数")
    @HBaseColumn(family = "i",qualifier = "dg1cp")
    private String dg1CosPhi;

    /**
     * 2号发电机功率因数 DG2-COS_PHI
     */
    @Excel(name="2号发电机功率因数")
    @HBaseColumn(family = "i",qualifier = "dg2cp")
    private String dg2CosPhi;

    /**
     * 3号发电机功率因数 DG3-COS_PHI
     */
    @Excel(name="3号发电机功率因数")
    @HBaseColumn(family = "i",qualifier = "dg3cp")
    private String dg3CosPhi;

    public PowerManageVo(){

    }

    public PowerManageVo(Map<String, EngineroomData> map){
        this.msbFrequency = AnalysisUtils.analysis(map.get("DG1-BUS_FREQ"));
        this.msbVoltageDg1 = AnalysisUtils.analysis(map.get("DG1-BUS-VOLT"));
        this.loadDg1 = AnalysisUtils.analysis(map.get("G1-LOAD"));
        this.dg1AvailablePower = AnalysisUtils.analysis(map.get("G1-AVA-POW"));
        this.dg1CurrentL1 = AnalysisUtils.analysis(map.get("G1-I-L1"));
        this.dg2CurrentL1 = AnalysisUtils.analysis(map.get("G2-I-L1"));
        this.dg3CurrentL1 = AnalysisUtils.analysis(map.get("G3-I-L1"));
        this.dg1Voltage = AnalysisUtils.analysis(map.get("DG1-VOLTAGE"));
        this.dg2Voltage = AnalysisUtils.analysis(map.get("DG2-VOLTAGE"));
        this.dg3Voltage = AnalysisUtils.analysis(map.get("DG3-VOLTAGE"));
        this.dg1Frequency = AnalysisUtils.analysis(map.get("DG1-FREQ"));
        this.dg2Frequency = AnalysisUtils.analysis(map.get("DG2-FREQ"));
        this.dg3Frequency = AnalysisUtils.analysis(map.get("DG3-FREQ"));
        this.dg1CosPhi = AnalysisUtils.analysis(map.get("DG1-COS_PHI"));
        this.dg2CosPhi = AnalysisUtils.analysis(map.get("DG2-COS_PHI"));
        this.dg3CosPhi = AnalysisUtils.analysis(map.get("DG3-COS_PHI"));

    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return bjTime;
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getMsbFrequency() {
        return msbFrequency;
    }

    public void setMsbFrequency(String msbFrequency) {
        this.msbFrequency = msbFrequency;
    }

    public String getMsbVoltageDg1() {
        return msbVoltageDg1;
    }

    public void setMsbVoltageDg1(String msbVoltageDg1) {
        this.msbVoltageDg1 = msbVoltageDg1;
    }

    public String getLoadDg1() {
        return loadDg1;
    }

    public void setLoadDg1(String loadDg1) {
        this.loadDg1 = loadDg1;
    }

    public String getDg1AvailablePower() {
        return dg1AvailablePower;
    }

    public void setDg1AvailablePower(String dg1AvailablePower) {
        this.dg1AvailablePower = dg1AvailablePower;
    }

    public String getDg1CurrentL1() {
        return dg1CurrentL1;
    }

    public void setDg1CurrentL1(String dg1CurrentL1) {
        this.dg1CurrentL1 = dg1CurrentL1;
    }

    public String getDg2CurrentL1() {
        return dg2CurrentL1;
    }

    public void setDg2CurrentL1(String dg2CurrentL1) {
        this.dg2CurrentL1 = dg2CurrentL1;
    }

    public String getDg3CurrentL1() {
        return dg3CurrentL1;
    }

    public void setDg3CurrentL1(String dg3CurrentL1) {
        this.dg3CurrentL1 = dg3CurrentL1;
    }

    public String getDg1Voltage() {
        return dg1Voltage;
    }

    public void setDg1Voltage(String dg1Voltage) {
        this.dg1Voltage = dg1Voltage;
    }

    public String getDg2Voltage() {
        return dg2Voltage;
    }

    public void setDg2Voltage(String dg2Voltage) {
        this.dg2Voltage = dg2Voltage;
    }

    public String getDg3Voltage() {
        return dg3Voltage;
    }

    public void setDg3Voltage(String dg3Voltage) {
        this.dg3Voltage = dg3Voltage;
    }

    public String getDg1Frequency() {
        return dg1Frequency;
    }

    public void setDg1Frequency(String dg1Frequency) {
        this.dg1Frequency = dg1Frequency;
    }

    public String getDg2Frequency() {
        return dg2Frequency;
    }

    public void setDg2Frequency(String dg2Frequency) {
        this.dg2Frequency = dg2Frequency;
    }

    public String getDg3Frequency() {
        return dg3Frequency;
    }

    public void setDg3Frequency(String dg3Frequency) {
        this.dg3Frequency = dg3Frequency;
    }

    public String getDg1CosPhi() {
        return dg1CosPhi;
    }

    public void setDg1CosPhi(String dg1CosPhi) {
        this.dg1CosPhi = dg1CosPhi;
    }

    public String getDg2CosPhi() {
        return dg2CosPhi;
    }

    public void setDg2CosPhi(String dg2CosPhi) {
        this.dg2CosPhi = dg2CosPhi;
    }

    public String getDg3CosPhi() {
        return dg3CosPhi;
    }

    public void setDg3CosPhi(String dg3CosPhi) {
        this.dg3CosPhi = dg3CosPhi;
    }
}
