package com.snct.hbase.service;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * HBase分页工具类
 * 用于根据RowKey列表计算分页边界
 *
 * <AUTHOR>
 */
@Component
public class PageUtils {
    /**
     * 计算分页边界RowKey
     * 根据完整RowKey列表和分页参数，计算当前页的起始和结束RowKey
     *
     * @param rowKeyList  完整的RowKey列表
     * @param currentPage 当前页码（从1开始）
     * @param pageSize    每页数据量
     * @return 包含以下信息的Map：
     *         - sRowKey: 起始RowKey，对应当前页第一条数据
     *         - eRowKey: 结束RowKey，对应当前页后一页第一条数据（用于HBase扫描边界）
     *         - total: 总记录数
     *         - currentPage: 当前页码
     *         - pageSize: 每页大小
     */
    public Map<String, String> getSERowKey(List<String> rowKeyList, int currentPage, int pageSize, String sortOrder) {
        Map<String, String> pageInfo = new HashMap<>();
        
        // 设置分页基本信息
        pageInfo.put("currentPage", String.valueOf(currentPage));
        pageInfo.put("pageSize", String.valueOf(pageSize));
        pageInfo.put("sortOrder", sortOrder);
        
        // 处理空列表情况
        if (rowKeyList == null || rowKeyList.isEmpty()) {
            pageInfo.put("total", "0");
            pageInfo.put("sRowKey", "");
            pageInfo.put("eRowKey", "");
            return pageInfo;
        }
        
        // 记录总数
        pageInfo.put("total", String.valueOf(rowKeyList.size()));
        
        // 计算起始索引
        int startIndex = (currentPage - 1) * pageSize;
        if (startIndex >= rowKeyList.size()) {
            // 如果起始索引超出范围，重置为第一页
            startIndex = 0;
        }

        // 计算结束索引
        int endIndex = startIndex + pageSize - 1;

        // 设置起始RowKey
        pageInfo.put("sRowKey", rowKeyList.get(startIndex));

        // 设置结束RowKey
        if (endIndex >= rowKeyList.size()) {
            String lastRowKey = rowKeyList.get(rowKeyList.size() - 1);
            pageInfo.put("eRowKey", lastRowKey);
        } else {
            pageInfo.put("eRowKey", rowKeyList.get(endIndex));
        }

        return pageInfo;
    }
}
