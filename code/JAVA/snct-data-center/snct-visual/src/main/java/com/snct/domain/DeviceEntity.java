package com.snct.domain;

import com.snct.common.core.domain.BaseEntity;

/**
 * 设备实体
 * <AUTHOR>
 */
public class DeviceEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 船舶sn
     */
    private String sn;

    /**
     * 船舶名称
     */
    private String shipName;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备类型
     */
    private Integer type;

    /**
     * 设备编码
     */
    private String code;

    /**
     * 启用状态
     */
    private Integer enable;


    private String attributes;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getEnable() {
        return enable == null ? 0 : enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }
}
