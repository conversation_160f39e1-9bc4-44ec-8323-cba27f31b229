package com.snct.hbase.domain.engineroom.vo;

import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;
import com.snct.hbase.domain.engineroom.EngineroomData;
import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:fw_generator")
public class FwGeneratorVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    private Long time;

    private String bjTime;

    /**
     * 1号造水机盐度   11005
     */
    @HBaseColumn(family = "i", qualifier = "salinity1")
    private String salinity1;

    /**
     * 2号造水机盐度  11011
     */
    @HBaseColumn(family = "i", qualifier = "salinity2")
    private String salinity2;

    public FwGeneratorVo(){}

    public FwGeneratorVo(Map<String, EngineroomData> map){

        this.salinity1= AnalysisUtils.analysis(map.get("11005"));
        this.salinity2= AnalysisUtils.analysis(map.get("11011"));

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return bjTime;
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getSalinity1() {
        return salinity1;
    }

    public void setSalinity1(String salinity1) {
        this.salinity1 = salinity1;
    }

    public String getSalinity2() {
        return salinity2;
    }

    public void setSalinity2(String salinity2) {
        this.salinity2 = salinity2;
    }
}
