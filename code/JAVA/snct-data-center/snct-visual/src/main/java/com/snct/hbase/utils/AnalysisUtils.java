package com.snct.hbase.utils;

import com.snct.hbase.domain.engineroom.EngineroomData;

/**
 * <AUTHOR>
 * 解析操作工具类
 */
public class AnalysisUtils {

    public static String analysis(EngineroomData engineroomData) {
        return engineroomData.getStatus() + "|" + engineroomData.getSymbol() + "|" + engineroomData.getValue();
    }

    public static String analysisValue(EngineroomData engineroomData) {
        return engineroomData.getValue();
    }
}
