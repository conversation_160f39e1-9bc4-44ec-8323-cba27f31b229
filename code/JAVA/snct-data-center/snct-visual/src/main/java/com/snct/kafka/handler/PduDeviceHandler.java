package com.snct.kafka.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.snct.kafka.KafkaMessage;
import com.snct.system.domain.msg.BuMsgPdu;
import com.snct.system.domain.msg.BuMsgPduOut;
import com.snct.system.service.IBuMsgPduOutService;
import com.snct.system.service.IBuMsgPduService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * PDU设备数据处理器
 *
 * <AUTHOR>
 */
@Component
public class PduDeviceHandler extends AbstractDeviceHandler {

    @Autowired
    private IBuMsgPduService buMsgPduService;

    @Autowired
    private IBuMsgPduOutService buMsgPduOutService;

    /**
     * 用于存储当前活跃批次信息
     */
    private static final Map<Long, Long> deviceToPduMap = new ConcurrentHashMap<>();

    @Override
    protected boolean saveToMysql(JSONObject jsonObject, String sn, Map<String, Long> deviceInfo) {
        try {
            // 提取基本信息
            String batchCode = jsonObject.getString("batchCode");
            Long deviceId = jsonObject.getLong("deviceId");
            Long timestamp = jsonObject.getLong("timestamp");

            if (deviceId == null || StringUtils.isEmpty(batchCode)) {
                logger.error("PDU数据缺少必要字段: deviceId={}, batchCode={}", deviceId, batchCode);
                return false;
            }

            boolean pduSaved = false;
            boolean channelSaved = false;

            // 处理PDU主设备数据
            JSONObject pduDataJson = jsonObject.getJSONObject("pduData");
            if (pduDataJson != null) {
                pduSaved = savePduMainData(pduDataJson, deviceId, deviceInfo, batchCode);
            }

            // 处理PDU通道数据
            JSONArray channelDataArray = jsonObject.getJSONArray("channelData");
            if (channelDataArray != null && !channelDataArray.isEmpty()) {
                channelSaved = saveChannelData(channelDataArray, deviceId, deviceInfo, batchCode);
            }

            return pduSaved || channelSaved;
        } catch (Exception e) {
            logger.error("保存PDU数据到MySQL失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    protected boolean saveToHbase(KafkaMessage kafkaMessage) {
        try {
            // 保存到HBase
            storeService.save2Hbase(kafkaMessage);
            return true;
        } catch (Exception e) {
            logger.error("保存PDU数据到HBase失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存PDU主设备数据到MySQL
     *
     * @param pduDataJson PDU主设备数据JSON
     * @param deviceId 设备ID
     * @param deviceInfo 设备信息
     * @param batchCode 批次号
     * @return 是否保存成功
     */
    private boolean savePduMainData(JSONObject pduDataJson, Long deviceId, 
                                    Map<String, Long> deviceInfo, String batchCode) {
        try {
            BuMsgPdu pdu = new BuMsgPdu();
            pdu.setDeptId(deviceInfo.get("deptId"));
            pdu.setShipId(deviceInfo.get("shipId"));
            pdu.setDeviceId(deviceId);
            pdu.setElectric(pduDataJson.getDouble("electric"));
            pdu.setManage(pduDataJson.getDouble("manage"));
            pdu.setVoltage(pduDataJson.getDouble("voltage"));
            pdu.setYesPwoer(pduDataJson.getDouble("yesPwoer"));
            pdu.setNoPwoer(pduDataJson.getDouble("noPwoer"));
            pdu.setSeePwoer(pduDataJson.getDouble("seePwoer"));
            pdu.setPowerParam(pduDataJson.getLong("powerParam"));
            pdu.setBatchCode(batchCode);
            pdu.setStatus(0L); // 默认状态

            // 插入PDU记录
            buMsgPduService.insertBuMsgPdu(pdu);

            // 获取插入后的PDU ID
            Long pduId = pdu.getId();
            if (pduId != null) {
                // 存储设备ID和PDU ID的映射关系
                deviceToPduMap.put(deviceId, pduId);
                return true;
            } else {
                logger.error("保存PDU主设备数据到MySQL失败，无法获取PDU_ID");
                return false;
            }
        } catch (Exception e) {
            logger.error("保存PDU主设备数据到MySQL失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存PDU通道数据到MySQL
     *
     * @param channelDataArray PDU通道数据数组
     * @param deviceId 设备ID
     * @param deviceInfo 设备信息
     * @param batchCode 批次号
     * @return 是否保存成功
     */
    private boolean saveChannelData(JSONArray channelDataArray, Long deviceId, 
                                    Map<String, Long> deviceInfo, String batchCode) {
        try {
            int channelCount = 0;
            for (int i = 0; i < channelDataArray.size(); i++) {
                JSONObject channelJson = channelDataArray.getJSONObject(i);
                if (channelJson != null) {
                    BuMsgPduOut pduOut = new BuMsgPduOut();
                    pduOut.setDeptId(deviceInfo.get("deptId"));
                    pduOut.setDeviceId(deviceId);
                    pduOut.setOutIndex(channelJson.getLong("outIndex"));
                    pduOut.setElectric(channelJson.getDouble("electric"));
                    pduOut.setPower(channelJson.getDouble("power"));
                    pduOut.setOutStatus(channelJson.getLong("outStatus"));
                    pduOut.setBatchCode(batchCode);
                    pduOut.setStatus(0L); // 默认状态

                    buMsgPduOutService.insertBuMsgPduOut(pduOut);
                    channelCount++;
                }
            }

            if (channelCount > 0) {
                return true;
            } else {
                logger.warn("未保存任何PDU通道数据");
                return false;
            }
        } catch (Exception e) {
            logger.error("保存PDU通道数据失败: {}", e.getMessage(), e);
            return false;
        }
    }
} 