package com.snct.hbase.domain.snapshot;

import com.snct.hbase.domain.HbaseBaseEntity;

/**
 * 快照通道
 *
 * <AUTHOR>
 */
public class SnapshotChannelEntityHbase extends HbaseBaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 部门ID（岸上需要）
     */
    private Long deptId;

    private String sn;

    private String shipName;

    /**
     * 通道编号
     */
    private String code;

    /**
     * 通道名称
     */
    private String name;

    /**
     * 通道地址
     */
    private String address;

    /**
     * 历史数据保存时长（单位天）
     */
    private Integer storageTime;

    //202110新增的显示字段
    /**
     * 截屏时间
     */
    private Long operateTime;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 目录
     */
    private String directory;

    /**
     * 截屏连接状态 1成功 0失败
     * @return
     */
    private String status;

    /**
     * 分辨率(640*360)
     */
    private String resolvingPower;

    /**
     * 传输间隔(单位：秒)
     */
    private Integer compartment;

    /**
     * 传输间隔--展示
     */
    private String compartmentStr;

    /**
     * 优先级
     */
    private Integer cost;
    /**
     * 快照存储状态
     */
    private Integer trStatus;

    public Integer getTransferStatus() {
        return transferStatus;
    }

    public void setTransferStatus(Integer transferStatus) {
        this.transferStatus = transferStatus;
    }

    /**
     * 快照传输状态
     */
    private Integer transferStatus;

    public String getResolvingPower() {
        return resolvingPower;
    }

    public void setResolvingPower(String resolvingPower) {
        this.resolvingPower = resolvingPower;
    }

    public Integer getCompartment() {
        return compartment;
    }

    public void setCompartment(Integer compartment) {
        this.compartment = compartment;
    }

    public String getCompartmentStr() {
        if (this.compartment == null) {
            return "";
        }
        if (this.compartment >= 60) {
            return (this.compartment / 60) + "分钟";
        } else {
            return this.compartment + "秒钟";
        }
    }

    public void setCompartmentStr(String compartmentStr) {
        this.compartmentStr = compartmentStr;
    }

    public Integer getCost() {
        return cost;
    }

    public void setCost(Integer cost) {
        this.cost = cost;
    }

    public Integer getTrStatus() {
        return trStatus;
    }

    public void setTrStatus(Integer trStatus) {
        this.trStatus = trStatus;
    }

    public Long getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Long operateTime) {
        this.operateTime = operateTime;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getDirectory() {
        return directory;
    }

    public void setDirectory(String directory) {
        this.directory = directory;
    }

    public String getStatus() {
        if (this.status == null || this.status.equalsIgnoreCase("")) {
            return "连接失败";
        }
        if (this.status.equalsIgnoreCase("1")){
            return "连接成功";
        }
        return "连接失败";
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getStorageTime() {
        return storageTime;
    }

    public void setStorageTime(Integer storageTime) {
        this.storageTime = storageTime;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }
}
