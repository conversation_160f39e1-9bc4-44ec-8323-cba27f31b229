package com.snct.hbase.utils;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.*;
import org.apache.hadoop.hbase.client.Admin;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

/**
 * @Author: tsohan
 * @Descriptions: 加载Hbase连接
 */
@Component
public class HConnectionFactory implements InitializingBean {

    protected final org.slf4j.Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${hbase.zookeeper.quorum}")
    private String zkQuorum;

    @Value("${hbase.master}")
    private String hBaseMaster;

    @Value("${hbase.zookeeper.property.clientPort}")
    private String zkPort;

    @Value("${zookeeper.znode.parent}")
    private String znode;

    private static Configuration conf = HBaseConfiguration.create();

    public static Connection connection;

    @Override
    public void afterPropertiesSet() {
        conf.set("hbase.zookeeper.quorum", zkQuorum);
        conf.set("hbase.zookeeper.property.clientPort", zkPort);
        conf.set("zookeeper.znode.parent", znode);
        conf.set("hbase.master", hBaseMaster);
        try {
            connection = ConnectionFactory.createConnection(conf);
            logger.info("获取connectiont连接成功！");


//            try (Admin admin = HConnectionFactory.connection.getAdmin();) {
//                // 获取一个namespace的描述器
//                NamespaceDescriptor nsd = NamespaceDescriptor.create("namespace3").build();
//                admin.createNamespace(nsd);
//            } catch (IOException e) {
//                e.printStackTrace();
//                logger.error("创建命名空间失败！", e);
//            }
//
//
//            Set<String> familyColumn = new HashSet<>();
//            familyColumn.add("familyColumn1");
//            familyColumn.add("familyColumn2");
//
//            TableName tn = TableName.valueOf("namespace:test");
//            try (Admin admin = HConnectionFactory.connection.getAdmin();) {
//                HTableDescriptor htd = new HTableDescriptor(tn);
//                for (String fc : familyColumn) {
//                    HColumnDescriptor hcd = new HColumnDescriptor(fc);
//                    htd.addFamily(hcd);
//                }
//
//                admin.createTable(htd);
//            } catch (IOException e) {
//                e.printStackTrace();
//                logger.error("创建test表失败！", e);
//            }


//            TableName tn = TableName.valueOf("namespace:test");
//            try (Admin admin = HConnectionFactory.connection.getAdmin();) {
//                admin.disableTable(tn);
//                admin.deleteTable(tn);
//            } catch (IOException e) {
//                e.printStackTrace();
//                logger.error("删除test表失败！");
//            }



        } catch (IOException e) {
            e.printStackTrace();
            logger.error("获取connectiont连接失败！");
        }
    }

}
